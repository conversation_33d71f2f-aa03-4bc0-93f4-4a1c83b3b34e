# Manual Dune Data Fetch API

## Overview

This document describes the manual trigger API endpoint for Dune data fetching. This endpoint allows you to manually trigger the `FetchAllDuneData` method that normally runs on a scheduled basis.

## Endpoint

```
POST /api/dune/fetch-all
```

## Purpose

- Manually trigger data fetching for all Dune query bindings
- Useful for testing, debugging, or immediate data updates
- Provides immediate feedback about the operation status
- Runs the fetch operation asynchronously to avoid blocking the HTTP response

## Request

### Headers

- `Content-Type: application/json` (required)
- `X-API-Key: <your-api-key>` (optional, for additional security)

### Body

No request body is required. This is a simple POST request.

### Example Request

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-optional-api-key" \
  http://localhost:8080/api/dune/fetch-all
```

## Response

### Success Response (HTTP 200)

```json
{
  "status": "success",
  "message": "Dune data fetch triggered successfully",
  "query_count": 5,
  "duration": "2.5ms",
  "triggered_at": 1672531200,
  "triggered_by": "manual"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Operation status ("success" or "error") |
| `message` | string | Human-readable message describing the result |
| `query_count` | integer | Number of unique Dune query IDs that will be processed |
| `duration` | string | Time taken to prepare and trigger the operation |
| `triggered_at` | integer | Unix timestamp when the operation was triggered |
| `triggered_by` | string | Source of the trigger ("manual" for API calls) |

### Error Response (HTTP 500)

```json
{
  "error": "Failed to get query count",
  "status": "error"
}
```

## Behavior

1. **Immediate Response**: The API returns immediately after triggering the fetch operation
2. **Asynchronous Execution**: The actual data fetching runs in a background goroutine
3. **Query Count**: Returns the number of unique Dune query IDs that will be processed
4. **Logging**: All operations are logged with appropriate detail levels
5. **Error Handling**: Individual query failures don't stop the entire process

## Security

- **Optional API Key**: You can provide an `X-API-Key` header for basic access control
- **IP Logging**: Client IP and User-Agent are logged for audit purposes
- **CORS Enabled**: The endpoint respects the application's CORS configuration

## Monitoring

### Logs

The operation generates several log entries:

```
INFO Manual Dune data fetch triggered via API ip=127.0.0.1 user_agent="curl/7.68.0"
INFO Manual Dune data fetch triggered successfully query_count=5 response_time=2.5ms
INFO Starting scheduled Dune data fetch
INFO Fetching data for Dune queries query_count=5
INFO Completed scheduled Dune data fetch duration=45.2s
```

### Metrics

The operation is tracked by existing metrics:
- API request metrics (duration, status codes)
- External API call metrics (for Dune API calls)

## Use Cases

1. **Development & Testing**: Trigger data updates during development
2. **Debugging**: Force a fetch when investigating data issues
3. **Immediate Updates**: Get fresh data without waiting for the scheduled run
4. **Integration Testing**: Verify the fetch process works correctly
5. **Manual Intervention**: Recover from failed scheduled runs

## Rate Limiting

- The endpoint itself has no specific rate limiting
- The underlying Dune API calls include built-in delays (100ms between requests)
- Consider implementing rate limiting if this endpoint will be called frequently

## Error Scenarios

1. **Database Connection Issues**: Returns 500 error if unable to query binding count
2. **Panic Recovery**: Background goroutine includes panic recovery
3. **Individual Query Failures**: Logged but don't stop the overall process

## Testing

Use the provided test script:

```bash
# Basic test
./test_dune_fetch_all.sh

# Test with custom URL and API key
./test_dune_fetch_all.sh http://localhost:8080 your-api-key
```

## Integration

This endpoint integrates seamlessly with the existing Dune service infrastructure:
- Uses the same `FetchAllDuneData()` method as the scheduler
- Follows the same error handling and logging patterns
- Respects the same rate limiting and retry logic
- Stores data using the same database operations

## Future Enhancements

Potential improvements could include:
- More granular control (fetch specific query IDs)
- Real-time progress updates via WebSocket
- Batch operation status tracking
- Enhanced authentication and authorization
- Request queuing for high-frequency usage
