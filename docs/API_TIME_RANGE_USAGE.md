# Dune Projects Time Range API Usage

## Overview

The new time range API allows users to query Dune project data for flexible time periods and includes change rate calculations compared to the previous period.

## API Endpoints

### 1. Get Projects Data with Time Range

**Endpoint:** `GET /api/dune/projects/data`

**Description:** Retrieve data for all Twitter-Dune binding projects within a specified time range with change rate calculation.

**Parameters:**
- `start_time` (required): Start time as Unix timestamp
- `end_time` (required): End time as Unix timestamp

**Example Request:**
```bash
curl "http://localhost:8080/api/dune/projects/data?start_time=1703894400&end_time=1703980800"
```

**Example Response:**
```json
[
  {
    "project_name": "Example Project",
    "project_logo": "https://example.com/logo.png",
    "twitter_user_name": "example_user",
    "dune_query_id": "12345",
    "query_results": [
      {
        "query_date": "2023-12-30T00:00:00Z",
        "contract_interaction": 1500,
        "users": 300,
        "change_rate": 0.25
      }
    ]
  }
]
```

### 2. Get Yesterday's Data (Legacy)

**Endpoint:** `GET /api/dune/projects/yesterday`

**Description:** Retrieve yesterday's data for all binding projects (maintained for backward compatibility).

## Change Rate Calculation

The `change_rate` field represents the percentage change compared to the previous period:

- **Formula:** `(new_value - old_value) / old_value`
- **Example:** If current period has 1500 interactions and previous period had 1200, the change rate is `(1500 - 1200) / 1200 = 0.25` (25% increase)
- **Positive values:** Indicate growth
- **Negative values:** Indicate decline
- **null values:** Indicate no previous data available for comparison

## Time Range Examples

### Query Last 7 Days
```bash
START_TIME=$(date -d '7 days ago' +%s)
END_TIME=$(date +%s)
curl "http://localhost:8080/api/dune/projects/data?start_time=$START_TIME&end_time=$END_TIME"
```

### Query Specific Week
```bash
# Query data from Dec 23-29, 2023
START_TIME=1703289600  # 2023-12-23 00:00:00 UTC
END_TIME=1703894400    # 2023-12-30 00:00:00 UTC
curl "http://localhost:8080/api/dune/projects/data?start_time=$START_TIME&end_time=$END_TIME"
```

### Query Yesterday vs Previous Day
```bash
# This will automatically compare yesterday's data with the day before
YESTERDAY_START=$(date -d 'yesterday' +%s)
TODAY_START=$(date -d 'today' +%s)
curl "http://localhost:8080/api/dune/projects/data?start_time=$YESTERDAY_START&end_time=$TODAY_START"
```

## Error Responses

### Missing Parameters (400)
```json
{
  "error": "start_time and end_time are required parameters"
}
```

### Invalid Timestamp (400)
```json
{
  "error": "Invalid start_time parameter. Must be a valid Unix timestamp."
}
```

### Invalid Time Range (400)
```json
{
  "error": "end_time must be after start_time"
}
```

### Server Error (500)
```json
{
  "error": "Failed to get projects data"
}
```

## Testing

Run the provided test script to verify the API functionality:

```bash
./test_time_range_api.sh
```

Make sure your server is running before executing the tests:

```bash
# Start the server
make run
# or
go run cmd/main.go
```

## Migration Notes

- The old `/api/dune/projects/yesterday` endpoint is still available for backward compatibility
- New applications should use `/api/dune/projects/data` with appropriate time parameters
- The new endpoint provides more flexibility and includes change rate calculations
- All timestamps should be in Unix format (seconds since epoch)
- All dates are processed in UTC timezone