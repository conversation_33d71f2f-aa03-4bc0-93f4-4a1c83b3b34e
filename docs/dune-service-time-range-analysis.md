# Dune Service 时间范围数据分析功能

## 概述

`GetAllProjectsDataWithTimeRange` 方法提供了强大的时间范围数据查询和同比分析功能，能够：

1. 查询指定时间范围内所有项目的 Dune 数据
2. 自动计算同比变化率
3. 提供详细的性能指标和日志记录

## 功能特性

### 1. 时间范围查询
- 支持任意时间范围的数据查询
- 自动验证输入参数（开始时间不能晚于结束时间）
- 使用批量查询优化性能，避免 N+1 查询问题

### 2. 同比分析
- 自动计算对比时间段（前一个相同长度的时间段）
- 计算变化率：`(当前期间 - 对比期间) / 对比期间 * 100%`
- 支持合约交互数和用户数的聚合分析

### 3. 性能优化
- 使用 `GetBatchQueryResults` 批量获取数据
- 单次数据库查询获取所有相关数据
- 内存中进行数据聚合和计算

## 方法签名

```go
func (s *DuneService) GetAllProjectsDataWithTimeRange(startDate, endDate time.Time) ([]*ProjectDataResponse, error)
```

## 返回数据结构

```go
type ProjectDataResponse struct {
    ProjectName     string                     `json:"project_name"`
    ProjectLogo     string                     `json:"project_logo"`
    TwitterUserName string                     `json:"twitter_user_name"`
    DuneQueryID     string                     `json:"dune_query_id"`
    QueryResults    []*DuneQueryResultResponse `json:"query_results"`
}

type DuneQueryResultResponse struct {
    QueryDate           time.Time `json:"query_date"`
    ContractInteraction int       `json:"contract_interaction"`
    Users               int       `json:"users"`
    ChangeRate          *float64  `json:"change_rate,omitempty"` // 变化率百分比
}
```

## 使用示例

### 1. 查询最近7天数据
```go
startDate := time.Now().UTC().AddDate(0, 0, -7).Truncate(24 * time.Hour)
endDate := time.Now().UTC().Truncate(24 * time.Hour)

results, err := duneService.GetAllProjectsDataWithTimeRange(startDate, endDate)
if err != nil {
    log.Error().Err(err).Msg("Failed to get projects data")
    return
}

for _, project := range results {
    log.Info().
        Str("project_name", project.ProjectName).
        Str("twitter_user", project.TwitterUserName).
        Int("results_count", len(project.QueryResults)).
        Msg("Project data retrieved")

    for _, result := range project.QueryResults {
        changeRateStr := "N/A"
        if result.ChangeRate != nil {
            changeRateStr = fmt.Sprintf("%.2f%%", *result.ChangeRate)
        }

        log.Info().
            Time("date", result.QueryDate).
            Int("interactions", result.ContractInteraction).
            Int("users", result.Users).
            Str("change_rate", changeRateStr).
            Msg("Daily metrics")
    }
}
```

### 2. 月度数据分析
```go
// 查询当月数据
now := time.Now().UTC()
startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC)
endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)

results, err := duneService.GetAllProjectsDataWithTimeRange(startOfMonth, endOfMonth)
```

## 同比分析逻辑

### 时间段计算
```
查询时间段：2024-01-01 到 2024-01-07 (7天)
对比时间段：2023-12-24 到 2023-12-31 (前7天)

变化率计算：
- 当前期间总交互数：1000
- 对比期间总交互数：800
- 变化率：(1000 - 800) / 800 * 100% = 25%
```

### 边界情况处理
- **无对比数据**：当对比期间没有数据时，`ChangeRate` 为 `nil`
- **对比数据为0**：当对比期间数据为0时，不计算变化率
- **无当前数据**：返回空的 `QueryResults` 数组

## 日志记录

### 信息级别日志
```json
{
  "level": "info",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-07T00:00:00Z",
  "comparison_start_date": "2023-12-24T23:00:00Z",
  "comparison_end_date": "2023-12-31T23:00:00Z",
  "period_duration": "168h0m0s",
  "message": "Calculated comparison period for year-over-year analysis"
}
```

### 调试级别日志
```json
{
  "level": "debug",
  "dune_query_id": "5399608",
  "twitter_user_name": "example_user",
  "current_interactions": 1000,
  "current_users": 500,
  "comparison_interactions": 800,
  "comparison_users": 400,
  "results_count": 7,
  "message": "Processed binding data with comparison analysis"
}
```

## 错误处理

### 常见错误类型
1. **参数验证错误**：`start date cannot be after end date`
2. **数据库查询错误**：`failed to get all bindings: ...`
3. **批量查询错误**：`failed to get current period batch query results: ...`

### 错误处理策略
- 输入参数验证在方法开始时进行
- 数据库错误会立即返回，不会继续处理
- 单个项目的数据处理错误不会影响其他项目

## 性能考虑

### 优化措施
1. **批量查询**：使用 `GetBatchQueryResults` 减少数据库查询次数
2. **内存聚合**：在内存中进行数据聚合，避免复杂的 SQL 查询
3. **索引利用**：利用数据库索引优化查询性能

### 性能指标
- 单次查询可处理数百个项目的数据
- 查询时间与项目数量和时间范围成正比
- 内存使用量与返回数据量成正比

## 新增 API 端点

### 昨天数据对比 API

**端点**: `GET /api/dune/projects/yesterday-comparison`

**描述**: 获取昨天的数据并与指定日期进行对比分析

**参数**:
- `comparison_time` (必需): 对比时间的 Unix 时间戳

**示例请求**:
```bash
# 获取昨天数据并与7天前对比
COMPARISON_TIME=$(date -d '7 days ago' +%s)
curl "http://localhost:8080/api/dune/projects/yesterday-comparison?comparison_time=$COMPARISON_TIME"
```

**响应格式**:
```json
[
  {
    "project_name": "Example Project",
    "project_logo": "https://example.com/logo.png",
    "twitter_user_name": "example_user",
    "dune_query_id": "5399608",
    "query_result": {
      "query_date": "2025-07-09T00:00:00Z",
      "contract_interaction": 14556,
      "users": 6049,
      "change_rate": 25.5
    }
  }
]
```

### 数据结构变化

新增了 `query_result` 字段（单个结果）来替代 `query_results` 数组：

```go
type ProjectDataResponse struct {
    ProjectName     string                    `json:"project_name"`
    ProjectLogo     string                    `json:"project_logo"`
    TwitterUserName string                    `json:"twitter_user_name"`
    DuneQueryID     string                    `json:"dune_query_id"`
    QueryResults    []*DuneQueryResultResponse `json:"query_results,omitempty"` // 向后兼容
    QueryResult     *DuneQueryResultResponse  `json:"query_result,omitempty"`  // 新的单结果字段
}
```

### 时区处理

新方法自动处理 UTC+8 时区：
- 昨天的数据基于 UTC+8 时区计算
- 数据库查询转换为 UTC 时间范围
- 确保跨时区的准确性

## 使用场景对比

### 原有方法 vs 新方法

| 特性 | GetAllProjectsDataWithTimeRange | GetAllProjectsYesterdayDataWithComparison |
|------|--------------------------------|-------------------------------------------|
| 时间范围 | 任意时间范围 | 固定昨天 |
| 对比逻辑 | 自动计算前一个相同时间段 | 指定对比日期 |
| 返回格式 | 数组 (query_results) | 单个对象 (query_result) |
| 时区处理 | UTC | UTC+8 自动转换 |
| 适用场景 | 灵活的历史数据分析 | 日常监控和报告 |

## 扩展性

### 未来增强
1. **多维度变化率**：支持用户数变化率计算
2. **自定义对比期间**：允许指定自定义的对比时间段
3. **数据缓存**：对频繁查询的数据进行缓存
4. **分页支持**：对大量项目数据支持分页返回
5. **多时区支持**：支持更多时区的自动转换
