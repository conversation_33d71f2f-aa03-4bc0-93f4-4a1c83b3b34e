# All Projects Field Sum Comparison API 实现文档

## 概述

本文档描述了新增的 All Projects Field Sum Comparison API 的实现，该API允许用户获取所有项目的指定数据项的聚合sum值，并与前一个相同长度的时间段进行对比。

## 实现的功能

### 1. API 端点

```
GET /api/dune/all-projects/field-sum-comparison
```

### 2. 参数说明

**查询参数:**
- `field`: 要聚合的字段名称 (可选)
  - 支持传统字段: `contract_interaction`, `users`
  - 支持动态字段: 任何在category中定义的自定义字段
  - 如果不提供此参数，将返回所有可用字段的聚合数据
- `days`: 分析时间段的天数 (必需)
  - 必须是正整数
  - 例如: 7 表示最近7天

### 3. 响应格式

#### 3.1 指定字段时的响应格式

当提供 `field` 参数时，返回单个字段的聚合数据：

```json
{
  "field": "contract_interaction",
  "days": 7,
  "total_bindings_count": 25,
  "current_sum": 15420.5,
  "previous_sum": 12350.2,
  "change_rate": 24.86,
  "analysis_period": "2024-01-15 to 2024-01-22",
  "comparison_period": "2024-01-08 to 2024-01-15"
}
```

#### 3.2 不指定字段时的响应格式

当不提供 `field` 参数时，返回所有可用字段的聚合数据：

```json
{
  "days": 7,
  "total_bindings_count": 25,
  "fields_data": {
    "contract_interaction": {
      "current_sum": 15420.5,
      "previous_sum": 12350.2,
      "change_rate": 24.86
    },
    "users": {
      "current_sum": 8750.0,
      "previous_sum": 7200.0,
      "change_rate": 21.53
    },
    "total_value_locked": {
      "current_sum": 2500000.0,
      "previous_sum": 2100000.0,
      "change_rate": 19.05
    }
  },
  "analysis_period": "2024-01-15 to 2024-01-22",
  "comparison_period": "2024-01-08 to 2024-01-15"
}
```

**响应字段说明:**
- `field`: 查询的字段名称 (仅在指定字段时出现)
- `days`: 分析的天数
- `total_bindings_count`: 参与聚合的总binding数量
- `current_sum`: 当前时间段的字段sum值 (仅在指定字段时出现)
- `previous_sum`: 对比时间段的字段sum值 (仅在指定字段时出现)
- `change_rate`: 变化率百分比 (仅在指定字段时出现，可能为null)
- `fields_data`: 所有字段的聚合数据 (仅在不指定字段时出现)
  - 每个字段包含 `current_sum`, `previous_sum`, `change_rate`
- `analysis_period`: 当前分析时间段
- `comparison_period`: 对比时间段

## 实现细节

### 1. Handler 层实现

在 `internal/api/handlers.go` 中新增了 `GetAllProjectsFieldSumComparison` 方法：

**核心逻辑:**
1. 验证输入参数（field不为空, days > 0）
2. 调用DuneService的GetAllProjectsFieldSumComparison方法
3. 返回聚合结果

### 2. Service 层实现

在 `internal/services/dune_service.go` 中新增了 `GetAllProjectsFieldSumComparison` 方法：

**核心逻辑:**
1. 验证输入参数
2. 获取所有bindings
3. 计算时间范围（当前期间和对比期间）
4. 批量获取查询结果
5. 聚合所有bindings的指定字段数据
6. 计算变化率
7. 格式化时间段字符串

### 3. 路由配置

在 `internal/api/routes.go` 中添加了新的路由：

```go
api.GET("/dune/all-projects/field-sum-comparison", handler.GetAllProjectsFieldSumComparison)
```

## 使用示例

### 基本用法

#### 指定字段查询

```bash
# 获取所有项目最近7天的contract_interaction总和对比
curl "http://localhost:8080/api/dune/all-projects/field-sum-comparison?field=contract_interaction&days=7"

# 获取所有项目最近14天的users总和对比
curl "http://localhost:8080/api/dune/all-projects/field-sum-comparison?field=users&days=14"

# 获取所有项目最近30天的自定义字段总和对比
curl "http://localhost:8080/api/dune/all-projects/field-sum-comparison?field=total_value_locked&days=30"
```

#### 查询所有字段

```bash
# 获取所有项目最近7天的所有字段总和对比
curl "http://localhost:8080/api/dune/all-projects/field-sum-comparison?days=7"

# 获取所有项目最近14天的所有字段总和对比
curl "http://localhost:8080/api/dune/all-projects/field-sum-comparison?days=14"
```

### 测试脚本

项目根目录下提供了测试脚本 `test_all_projects_field_sum_api.sh`，可以运行各种测试用例：

```bash
./test_all_projects_field_sum_api.sh
```

## 与现有API的对比

| 特性 | GetCategoryFieldPeriodComparison | GetAllProjectsFieldSumComparison |
|------|----------------------------------|----------------------------------|
| **范围** | 特定分类下的bindings | 所有bindings |
| **输入参数** | categoryID, field, days | field, days |
| **数据来源** | 单个category的bindings | 全部bindings |
| **聚合方式** | 分类内聚合 | 全局聚合 |
| **响应信息** | 包含分类信息 | 包含全局统计信息 |

## 错误处理

API 提供了完善的错误处理：

1. **400 Bad Request**: 参数验证失败
   - days参数缺失、无效或小于等于0

2. **500 Internal Server Error**: 服务器内部错误
   - 数据库查询失败
   - 数据聚合过程中出错

## 性能考虑

1. **批量查询**: 使用 `GetBatchQueryResults` 避免N+1查询问题
2. **数据聚合**: 在内存中进行聚合，避免多次数据库查询
3. **字段灵活性**: 支持动态字段查询，使用通用的字段求和函数
4. **缓存友好**: 可以与现有的缓存机制集成

## 扩展性

1. **字段支持**: 支持任何数值类型的字段聚合
2. **时间段灵活**: 支持任意天数的时间段对比
3. **聚合方式**: 可以扩展支持其他聚合方式（平均值、最大值等）
4. **过滤条件**: 可以扩展支持按标签、链等条件过滤

## 注意事项

1. **数据一致性**: 确保所有bindings的数据都是最新的
2. **性能监控**: 当bindings数量很大时，需要监控查询性能
3. **字段验证**: 虽然支持动态字段，但建议验证字段的有效性
4. **时区处理**: 时间计算基于服务器时区，需要注意时区一致性