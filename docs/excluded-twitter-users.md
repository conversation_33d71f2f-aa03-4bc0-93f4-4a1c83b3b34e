# 排除 Twitter 用户功能

## 概述

为了解决 `GetAnnouncementStatistics` 方法返回所有数据库中存储的数据问题，我们添加了一个排除 Twitter 用户的功能。这个功能允许后台管理员手动标记某些 Twitter 用户，使他们不再出现在统计数据和 API 返回中。

## 功能特性

- **排除机制**: 被标记的 Twitter 用户将从 `GetAnnouncementStatistics` API 的结果中排除
- **管理接口**: 提供完整的 CRUD API 来管理排除列表
- **审计跟踪**: 记录排除原因、操作人员和时间
- **数据完整性**: 原始数据保持不变，只是在查询时过滤

## 数据库结构

### excluded_twitter_users 表

```sql
CREATE TABLE excluded_twitter_users (
    id BIGSERIAL PRIMARY KEY,
    twitter_user_name VARCHAR(255) NOT NULL UNIQUE,
    reason TEXT,
    excluded_by VARCHAR(255),
    excluded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## API 接口

### 1. 添加排除用户

**POST** `/api/excluded-twitter-users`

```json
{
  "twitter_user_name": "username_to_exclude",
  "reason": "不再追踪此项目",
  "excluded_by": "admin"
}
```

**响应:**
```json
{
  "status": "success",
  "message": "Twitter user added to exclusion list"
}
```

### 2. 获取排除用户列表

**GET** `/api/excluded-twitter-users`

**响应:**
```json
[
  {
    "id": 1,
    "twitter_user_name": "username_to_exclude",
    "reason": "不再追踪此项目",
    "excluded_by": "admin",
    "excluded_at": "2025-01-21T10:30:00Z"
  }
]
```

### 3. 移除排除用户

**DELETE** `/api/excluded-twitter-users/{twitter_user_name}`

**响应:**
```json
{
  "status": "success",
  "message": "Twitter user removed from exclusion list"
}
```

## 影响的功能

### GetAnnouncementStatistics API

现在会自动排除在 `excluded_twitter_users` 表中的用户：

```sql
-- 修改后的查询会包含这个条件
LEFT JOIN excluded_twitter_users ON twitter_users.screen_name = excluded_twitter_users.twitter_user_name
WHERE excluded_twitter_users.twitter_user_name IS NULL
```

## 使用示例

### 排除一个不再追踪的项目

```bash
curl -X POST "http://localhost:8080/api/excluded-twitter-users" \
  -H "Content-Type: application/json" \
  -d '{
    "twitter_user_name": "old_project",
    "reason": "项目已停止，不再追踪",
    "excluded_by": "admin"
  }'
```

### 查看当前排除列表

```bash
curl -X GET "http://localhost:8080/api/excluded-twitter-users"
```

### 恢复一个用户

```bash
curl -X DELETE "http://localhost:8080/api/excluded-twitter-users/old_project"
```

## 测试

运行测试脚本验证功能：

```bash
./test_excluded_twitter_users.sh
```

## 注意事项

1. **数据保留**: 排除用户不会删除原始数据，只是在查询时过滤
2. **唯一性**: 每个 Twitter 用户名只能有一条排除记录
3. **更新机制**: 重复添加同一用户会更新现有记录
4. **大小写敏感**: Twitter 用户名匹配是大小写敏感的
5. **即时生效**: 排除/恢复操作立即生效，无需重启服务

## 数据库迁移

新功能需要运行数据库迁移：

```sql
-- 运行 migrations/004_add_excluded_twitter_users_table.sql
```

## 监控和日志

所有排除用户的操作都会记录在应用日志中，包括：
- 添加排除用户的操作
- 移除排除用户的操作
- 查询排除列表的操作
- 相关错误信息