# Aggregate Sum Feature Documentation

## Overview

This feature adds support for configurable field comparison logic in Dune category fields. You can now control whether a field's comparison should use sum aggregation or latest value comparison.

## Database Changes

### New Field: `aggregate_sum`

A new boolean field `aggregate_sum` has been added to the `dune_category_fields` table:

- **Type**: `boolean`
- **Default**: `true` (for backward compatibility)
- **Description**: Controls whether field comparison uses sum aggregation (true) or latest value comparison (false)

### Migration

Run the migration to add the new field:

```sql
-- Migration: 005_add_aggregate_sum_field_to_dune_category_fields.sql
ALTER TABLE public.dune_category_fields 
ADD COLUMN IF NOT EXISTS aggregate_sum boolean DEFAULT true NOT NULL;
```

## API Changes

### DuneCategoryFieldRequest

The API request structure now includes the `aggregate_sum` field:

```json
{
  "key": "total_value_locked",
  "type": "float",
  "note": "Total Value Locked in USD",
  "required": true,
  "aggregate_sum": true
}
```

### Example API Usage

#### Creating a Category with Mixed Field Types

```bash
curl -X POST "http://localhost:8080/api/dune/categories" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "defi-metrics",
    "description": "DeFi protocol metrics with mixed aggregation types",
    "fields": [
      {
        "key": "total_volume",
        "type": "float",
        "note": "Total trading volume (sum across period)",
        "required": true,
        "aggregate_sum": true
      },
      {
        "key": "current_price",
        "type": "float",
        "note": "Current token price (latest value)",
        "required": true,
        "aggregate_sum": false
      },
      {
        "key": "total_users",
        "type": "int",
        "note": "Total unique users (sum across period)",
        "required": true,
        "aggregate_sum": true
      }
    ]
  }'
```

## Behavior Changes

### Aggregation Logic

#### When `aggregate_sum = true` (Default)
- **Current Period**: Sum all values across the period
- **Comparison Period**: Sum all values across the comparison period
- **Change Rate**: Compare the two sums

#### When `aggregate_sum = false` (New)
- **Current Period**: Use the latest (most recent) value from the period
- **Comparison Period**: Use the latest (most recent) value from the comparison period
- **Change Rate**: Compare the two latest values

### Example Scenarios

#### Scenario 1: Trading Volume (aggregate_sum = true)
```
Current Period Data:
- Day 1: 1000
- Day 2: 1500
- Day 3: 2000
Current Value: 4500 (sum)

Comparison Period Data:
- Day 1: 800
- Day 2: 1200
- Day 3: 1500
Comparison Value: 3500 (sum)

Change Rate: (4500 - 3500) / 3500 * 100 = 28.57%
```

#### Scenario 2: Token Price (aggregate_sum = false)
```
Current Period Data:
- Day 1: $10.50
- Day 2: $11.20
- Day 3: $12.00
Current Value: $12.00 (latest)

Comparison Period Data:
- Day 1: $9.80
- Day 2: $10.10
- Day 3: $10.50
Comparison Value: $10.50 (latest)

Change Rate: (12.00 - 10.50) / 10.50 * 100 = 14.29%
```

## Response Format

The API response format remains the same, but the values in `data`, `comparison_data`, and `data_change_rates` will reflect the appropriate aggregation method for each field.

### Example Response

```json
{
  "id": 1,
  "project_name": "Example DeFi Protocol",
  "query_result": {
    "query_date": "2024-01-15T00:00:00Z",
    "data": {
      "total_volume": 4500.0,     // Sum aggregated
      "current_price": 12.0,      // Latest value
      "total_users": 150          // Sum aggregated
    },
    "comparison_data": {
      "total_volume": 3500.0,     // Sum aggregated
      "current_price": 10.5,      // Latest value
      "total_users": 120          // Sum aggregated
    },
    "data_change_rates": {
      "total_volume": 28.57,      // Based on sum comparison
      "current_price": 14.29,     // Based on latest value comparison
      "total_users": 25.0         // Based on sum comparison
    }
  }
}
```

## Backward Compatibility

This feature is fully backward compatible:

1. **Default Value**: All existing fields default to `aggregate_sum = true`
2. **Existing Behavior**: Fields without explicit configuration maintain sum aggregation
3. **API Compatibility**: The `aggregate_sum` field is optional in API requests

## Use Cases

### Sum Aggregation (`aggregate_sum = true`)
- Trading volume
- Transaction count
- Total fees collected
- User activity metrics
- Revenue metrics

### Latest Value Comparison (`aggregate_sum = false`)
- Token prices
- Market cap
- TVL (Total Value Locked)
- Exchange rates
- Snapshot metrics
- Current balances

## Implementation Notes

### Code Changes

1. **Database Model**: Added `AggregateSum` field to `DuneCategoryField`
2. **API Handlers**: Updated request/response structures
3. **Aggregation Logic**: Modified `aggregateDynamicData` function
4. **Change Rate Calculation**: New `calculateDynamicFieldChangeRatesWithLatestComparison` function
5. **Comparison Data**: New `generateComparisonData` function for accurate response data

### Performance Considerations

- Latest value comparison is more efficient as it doesn't require summing all values
- The system automatically selects the most recent record for non-aggregate fields
- Aggregation behavior is determined at query time based on field configuration