# Category Period Comparison API 实现文档

## 概述

本文档描述了新增的 Category Period Comparison API 的实现，该API允许用户获取指定分类下所有绑定的聚合数据，并与前一个相同长度的时间段进行对比。

## 实现的功能

### 1. API 端点

```
GET /api/dune/categories/{id}/period-comparison
```

### 2. 参数说明

**路径参数:**
- `id`: 分类 ID (必需)

**查询参数:**
- `field`: 要分析的字段名称 (必需)
  - 支持传统字段: `contract_interaction`, `users`
  - 支持动态字段: 根据分类定义的任何字段
- `days`: 分析时间段的天数 (必需)

### 3. 响应结构

```json
{
  "category_id": 1,
  "category_name": "DeFi Protocols",
  "category_description": "Decentralized Finance protocol metrics",
  "field": "contract_interaction",
  "days": 7,
  "bindings_count": 5,
  "current_sum": 15420.0,
  "previous_sum": 12350.0,
  "change_rate": 24.86
}
```

## 技术实现

### 1. 数据模型

新增了 `CategoryFieldPeriodComparisonResponse` 结构体：

```go
type CategoryFieldPeriodComparisonResponse struct {
    CategoryID          int64    `json:"category_id"`
    CategoryName        string   `json:"category_name"`
    CategoryDescription string   `json:"category_description"`
    Field               string   `json:"field"`
    Days                int      `json:"days"`
    BindingsCount       int      `json:"bindings_count"`
    CurrentSum          float64  `json:"current_sum"`
    PreviousSum         float64  `json:"previous_sum"`
    ChangeRate          *float64 `json:"change_rate,omitempty"`
}
```

### 2. Service 层实现

在 `internal/services/dune_service.go` 中新增了 `GetCategoryFieldPeriodComparison` 方法：

**核心逻辑:**
1. 验证输入参数（categoryID > 0, field不为空, days > 0）
2. 获取category信息
3. 获取该category下的所有bindings
4. 计算时间范围（当前期间和对比期间）
5. 批量获取查询结果
6. 聚合所有bindings的数据
7. 计算变化率

**关键特性:**
- 重用现有的 `GetBindingsByCategory` 和 `GetBatchQueryResults` 方法
- 支持传统字段和动态字段的聚合
- 使用与 `GetBindingFieldPeriodComparison` 相同的时间计算逻辑
- 支持多个bindings的数据聚合

### 3. Handler 层实现

在 `internal/api/handlers.go` 中新增了 `GetCategoryFieldPeriodComparison` handler：

**功能:**
- 参数验证和解析
- 错误处理（区分not found和其他错误）
- 调用service层方法
- 返回JSON响应

### 4. 路由配置

在 `internal/api/routes.go` 中添加了新的路由：

```go
api.GET("/dune/categories/:id/period-comparison", handler.GetCategoryFieldPeriodComparison)
```

### 5. API 文档

- 更新了 Swagger 注释
- 自动生成了 API 文档
- 添加了完整的参数和响应说明

## 使用示例

### 基本用法

```bash
# 获取分类1在过去7天的contract_interaction数据对比
curl -X GET "http://localhost:8080/api/dune/categories/1/period-comparison?field=contract_interaction&days=7"

# 获取分类1在过去30天的users数据对比
curl -X GET "http://localhost:8080/api/dune/categories/1/period-comparison?field=users&days=30"
```

### 动态字段查询

```bash
# 查询自定义字段（如果分类定义了该字段）
curl -X GET "http://localhost:8080/api/dune/categories/1/period-comparison?field=custom_metric&days=14"
```

## 测试

创建了专门的测试脚本 `test/scripts/test_category_period_comparison_api.sh`，包含：

1. 参数验证测试（缺失参数、无效参数）
2. 错误情况测试（不存在的分类）
3. 正常功能测试（不同时间段、不同字段）
4. 边界情况测试

## 与现有API的关系

### 相似性
- 与 `GetBindingFieldPeriodComparison` 使用相同的时间计算逻辑
- 支持相同的字段类型（传统字段和动态字段）
- 使用相同的数据聚合方法

### 差异性
- **聚合范围**: binding级别 vs category级别
- **响应结构**: 包含category信息而非单个binding信息
- **数据来源**: 单个binding vs 多个bindings的聚合

## 错误处理

API 提供了完善的错误处理：

1. **400 Bad Request**: 参数验证失败
2. **404 Not Found**: 分类不存在或没有关联的bindings
3. **500 Internal Server Error**: 服务器内部错误

## 性能考虑

1. **批量查询**: 使用 `GetBatchQueryResults` 避免N+1查询问题
2. **数据聚合**: 在内存中进行聚合，避免多次数据库查询
3. **缓存友好**: 可以与现有的缓存机制集成

## 扩展性

该实现具有良好的扩展性：

1. **字段支持**: 自动支持新增的动态字段
2. **聚合方式**: 可以轻松扩展支持其他聚合函数（平均值、最大值等）
3. **时间范围**: 可以扩展支持自定义时间范围

## 总结

Category Period Comparison API 成功实现了分类级别的数据聚合和时间对比功能，为用户提供了更高层次的数据分析能力。该实现遵循了现有的代码架构和设计模式，确保了代码的一致性和可维护性。
