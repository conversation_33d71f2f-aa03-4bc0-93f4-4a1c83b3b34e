// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/announcement-statistics/hide-list": {
            "get": {
                "description": "Get the full list of hidden usernames for announcement statistics",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Get announcement statistics hide list",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "post": {
                "description": "Overwrite the hide user list for announcement statistics with the provided usernames array. The existing list is cleared and replaced.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Set announcement statistics hide list",
                "parameters": [
                    {
                        "description": "Usernames to set as hidden",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.HideListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/announcement-statistics": {
            "get": {
                "description": "Get statistics for tweets with source_list_type=\"Projects\" and ai_judgment=\"YES\" grouped by Twitter user. Optional time filtering with start_time and end_time query parameters (Unix timestamps). Optional sorting with sort_field and sort_direction parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Get announcement statistics",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 1640995200,
                        "description": "Start time (Unix timestamp, inclusive)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "example": 1672531199,
                        "description": "End time (Unix timestamp, inclusive)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "product_updates",
                            "business_data",
                            "ecosystem_partnership",
                            "profit_opportunity",
                            "industry_events",
                            "others",
                            "total",
                            "days_since_last_tweet"
                        ],
                        "type": "string",
                        "description": "Field to sort by",
                        "name": "sort_field",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "description": "Sort direction",
                        "name": "sort_direction",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.UserAnnouncementStatsResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/announcement-statistics/daily": {
            "get": {
                "description": "Get daily announcement statistics history starting from the day before current day (UTC+8 timezone)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Get daily announcement statistics",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of days to look back (default: 7)",
                        "name": "days",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.AnnouncementStatisticsDailyResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/announcement-statistics/total": {
            "get": {
                "description": "Get total announcement count and 24-hour change percentage for all projects",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "statistics"
                ],
                "summary": "Get total announcement statistics",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.AnnouncementStatisticsTotalResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/cached-collections": {
            "get": {
                "description": "Retrieves collections data from Redis cache with tags information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "collections"
                ],
                "summary": "Get cached collections data",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Page number (default: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Page size (default: 100)",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.GetTradeTokenInfoResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/collection-tags": {
            "get": {
                "description": "Returns a list of all unique collection tags from cached collections",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "collections"
                ],
                "summary": "Get all collection tags",
                "responses": {
                    "200": {
                        "description": "Collection tags list",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/dune/all-projects/daily-field-sum": {
            "get": {
                "description": "Retrieve daily aggregated sum data for all binding projects within specified days range. Returns daily sum values for each field without comparison.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get all projects daily field sum",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields",
                        "name": "field",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days, 30 for last 30 days, 0 for all available data)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "All projects daily field sum data",
                        "schema": {
                            "$ref": "#/definitions/services.AllProjectsDailyFieldSumResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/all-projects/field-sum-comparison": {
            "get": {
                "description": "Retrieve aggregated sum data for all binding projects within specified days range and compare with the previous period of the same length. Returns sum values across all projects for the specified field.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get all projects field sum comparison",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields",
                        "name": "field",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "All projects field sum comparison data",
                        "schema": {
                            "$ref": "#/definitions/services.AllProjectsFieldSumComparisonResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/bindings": {
            "get": {
                "description": "Retrieve all Dune Twitter bindings with pagination support",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get all Dune Twitter bindings",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of bindings to return (default: 100)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of bindings to skip (default: 0)",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of Dune Twitter bindings",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.DuneBindingResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to get bindings",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Create new bindings between Dune queries and a Twitter user with category associations",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Create new Dune Twitter bindings",
                "parameters": [
                    {
                        "description": "Dune binding request with category-query mappings",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.DuneBindingRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Bindings created successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to create bindings",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/bindings/{id}": {
            "get": {
                "description": "Retrieve a specific Dune Twitter binding by its ID with category-query mappings",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get a specific Dune Twitter binding",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Dune Twitter binding with category queries",
                        "schema": {
                            "$ref": "#/definitions/db.DuneBindingResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid binding ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Binding not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get binding",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "description": "Update an existing Dune Twitter binding by its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Update a Dune Twitter binding",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated binding data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.DuneBindingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Binding updated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid binding ID or request body",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to update binding",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "description": "Delete a Dune Twitter binding by its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Delete a Dune Twitter binding",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Binding deleted successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid binding ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to delete binding",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/bindings/{id}/categories": {
            "get": {
                "description": "Get a specific binding with its associated categories and field definitions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-bindings"
                ],
                "summary": "Get binding with categories",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Binding with categories",
                        "schema": {
                            "$ref": "#/definitions/db.DuneTwitterBinding"
                        }
                    },
                    "400": {
                        "description": "Invalid binding ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Binding not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get binding",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "description": "Associate a dune binding with multiple categories",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-bindings"
                ],
                "summary": "Associate binding with categories",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Category association request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.DuneBindingCategoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Association successful",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Binding not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to associate categories",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/bindings/{id}/period-comparison": {
            "get": {
                "description": "Retrieve aggregated data for a specific binding within specified days range and compare with the previous period of the same length. Returns the same data structure as GetAllProjectsDataWithPeriodComparison but for a single binding. If field parameter is provided, only that field will be included in data and data_change_rates.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get binding project period comparison",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Dune binding ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Optional field name to filter data (if provided, only this field will be included in data and data_change_rates)",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Binding project aggregated data with period comparison",
                        "schema": {
                            "$ref": "#/definitions/services.ProjectDataResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Binding not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get project data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories": {
            "get": {
                "description": "Retrieve all dune categories with their field definitions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get all dune categories",
                "responses": {
                    "200": {
                        "description": "List of categories",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.DuneCategory"
                            }
                        }
                    },
                    "500": {
                        "description": "Failed to get categories",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new category with field definitions for dune data parsing",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Create a new dune category",
                "parameters": [
                    {
                        "description": "Category creation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.DuneCategoryRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Category created successfully",
                        "schema": {
                            "$ref": "#/definitions/db.DuneCategory"
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to create category",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}": {
            "get": {
                "description": "Retrieve a specific dune category by its ID with field definitions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get a specific dune category",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Dune category",
                        "schema": {
                            "$ref": "#/definitions/db.DuneCategory"
                        }
                    },
                    "400": {
                        "description": "Invalid category ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get category",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "description": "Update an existing dune category and its field definitions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Update a dune category",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated category data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.DuneCategoryRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Category updated successfully",
                        "schema": {
                            "$ref": "#/definitions/db.DuneCategory"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to update category",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "delete": {
                "description": "Delete a dune category and all its associations",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Delete a dune category",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Category deleted successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid category ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to delete category",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}/bindings": {
            "get": {
                "description": "Get all bindings associated with a specific category",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get bindings by category",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of bindings",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.DuneTwitterBinding"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid category ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get bindings",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}/daily-field-sum": {
            "get": {
                "description": "Retrieve daily aggregated sum data for binding projects in a specific category within specified days range. Returns daily sum values for each field without comparison.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get category projects daily field sum",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields",
                        "name": "field",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days, 30 for last 30 days, 0 for all available data)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Category projects daily field sum data",
                        "schema": {
                            "$ref": "#/definitions/services.CategoryDailyFieldSumResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}/field-sum-comparison": {
            "get": {
                "description": "Retrieve aggregated sum data for binding projects in a specific category within specified days range and compare with the previous period of the same length. Returns sum values for the specified field or all fields if field is not provided.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get category projects field sum comparison",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields",
                        "name": "field",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Category projects field sum comparison data",
                        "schema": {
                            "$ref": "#/definitions/services.CategoryFieldSumComparisonResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}/period-comparison": {
            "get": {
                "description": "Retrieve aggregated data for binding projects in a specific category within specified days range and compare with the previous period of the same length. Returns the same data structure as GetAllProjectsDataWithPeriodComparison but filtered to category bindings. If field parameter is provided, only that field will be included in data and data_change_rates.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune-categories"
                ],
                "summary": "Get category projects period comparison",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Optional field name to filter data (if provided, only this field will be included in data and data_change_rates)",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Category projects aggregated data with period comparison",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/services.ProjectDataResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Category not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/categories/{id}/project-data/{user_name}": {
            "get": {
                "description": "Retrieve Dune project data for a specific Twitter user and category ID with optional time range filtering",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get project data by Twitter user and category",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Twitter username",
                        "name": "user_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Category ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "format": "int64",
                        "description": "Start time as Unix timestamp",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "format": "int64",
                        "description": "End time as Unix timestamp",
                        "name": "end_time",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Project data",
                        "schema": {
                            "$ref": "#/definitions/services.ProjectDataResponse"
                        }
                    },
                    "400": {
                        "description": "Bad request - invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/fetch-all": {
            "post": {
                "description": "Manually triggers the FetchAllDuneData method to fetch data for all Dune query bindings",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Manually trigger Dune data fetch",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Optional API key for access control",
                        "name": "X-API-Key",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Fetch triggered successfully",
                        "schema": {
                            "$ref": "#/definitions/api.DuneFetchAllResponse"
                        }
                    },
                    "500": {
                        "description": "Failed to trigger fetch",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/project-data/:id": {
            "get": {
                "description": "Retrieve project data from Dune Analytics by Twitter username and time range",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get Dune project data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Query ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Start time (Unix timestamp)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End time (Unix timestamp)",
                        "name": "end_time",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Project data from Dune Analytics",
                        "schema": {
                            "$ref": "#/definitions/services.ProjectDataResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get project data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/dune/projects/period-comparison": {
            "get": {
                "description": "Retrieve aggregated data for all Twitter-Dune binding projects within specified days range and compare with previous period",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "dune"
                ],
                "summary": "Get aggregated data for all Dune projects with period comparison",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of days for the analysis period (e.g., 7 for last 7 days)",
                        "name": "days",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Projects aggregated data with period comparison",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/services.ProjectDataResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Failed to get projects data",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/excluded-twitter-users": {
            "get": {
                "description": "Retrieves all excluded Twitter users",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "excluded-users"
                ],
                "summary": "Get excluded Twitter users",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.ExcludedTwitterUser"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "post": {
                "description": "Adds a Twitter user to the exclusion list for statistics and API returns",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "excluded-users"
                ],
                "summary": "Add excluded Twitter user",
                "parameters": [
                    {
                        "description": "Excluded user request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.ExcludedTwitterUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/excluded-twitter-users/{twitter_user_name}": {
            "delete": {
                "description": "Removes a Twitter user from the exclusion list",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "excluded-users"
                ],
                "summary": "Remove excluded Twitter user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Twitter user name",
                        "name": "twitter_user_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Returns 200 OK if the service is healthy",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "system"
                ],
                "summary": "Health check endpoint",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/list-tweets": {
            "get": {
                "description": "Retrieves a list of tweets related to AI Agent with filtering options",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get AI Agent related tweets",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of tweets to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by content type (tweet, article, ALL)",
                        "name": "content_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by source type (KOLs, Projects, ALL)",
                        "name": "source_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by notice_type",
                        "name": "notice_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user_id",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by user_name",
                        "name": "user_name",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "collection_tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.TweetResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/recognized-ca/{address}/{chain_id}": {
            "get": {
                "description": "Retrieves a specific recognized contract address by its address and chain ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "contract-addresses"
                ],
                "summary": "Get a single recognized contract address",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Contract address",
                        "name": "address",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chain ID",
                        "name": "chain_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.CAResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/recognized-cas": {
            "get": {
                "description": "Retrieves a list of recognized contract addresses",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "contract-addresses"
                ],
                "summary": "Get recognized contract addresses",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of CAs to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.CAResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/telegram-notification-stats": {
            "get": {
                "description": "Returns statistics about Telegram notifications including total sent, duplicates prevented, etc.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Get Telegram notification statistics",
                "responses": {
                    "200": {
                        "description": "Notification statistics",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/trigger-collections-cache": {
            "post": {
                "description": "Triggers the collections cache update task manually for testing purposes",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "system"
                ],
                "summary": "Manually trigger collections cache update",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/tweet/{tweet_id}": {
            "get": {
                "description": "Retrieves a single tweet by its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get a tweet by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Tweet ID",
                        "name": "tweet_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.TweetResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/tweets": {
            "get": {
                "description": "Retrieves a list of tweets",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tweets"
                ],
                "summary": "Get tweets",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Number of tweets to return (default: 20)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "tags",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')",
                        "name": "collection_tags",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.TweetResponse"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/webhook/twitter": {
            "post": {
                "description": "Processes webhook events from Twitter",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "webhooks"
                ],
                "summary": "Handle Twitter webhook",
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        }
    },
    "definitions": {
        "api.AnnouncementStatisticsDailyResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "date": {
                    "type": "string"
                }
            }
        },
        "api.AnnouncementStatisticsTotalResponse": {
            "type": "object",
            "properties": {
                "change_percentage_24h": {
                    "type": "number"
                },
                "previous_day_total": {
                    "type": "integer"
                },
                "total_announcements": {
                    "type": "integer"
                }
            }
        },
        "api.CAResponse": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "chain_type": {
                    "type": "string"
                },
                "is_recognized": {
                    "type": "boolean"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "token_details": {
                    "description": "Changed to array",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TokenDetailsResponse"
                    }
                },
                "trade_token_details": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TradeTokenInfo"
                    }
                },
                "tweets": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.TweetInfo"
                    }
                }
            }
        },
        "api.DuneBindingCategoryRequest": {
            "type": "object",
            "required": [
                "category_ids"
            ],
            "properties": {
                "category_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "api.DuneBindingRequest": {
            "description": "Request body for creating or updating a Dune Twitter binding with category-query mappings",
            "type": "object",
            "required": [
                "category_queries",
                "twitter_user_name"
            ],
            "properties": {
                "category_queries": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.CategoryQueryMapping"
                    }
                },
                "chain_names": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "base",
                        "bsc"
                    ]
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "0x1234567890123456789012345678901234567890"
                    ]
                },
                "project_logo": {
                    "type": "string",
                    "example": "https://pbs.twimg.com/profile_images/1637832152000000000/1637832152000000000.jpg"
                },
                "project_name": {
                    "type": "string",
                    "example": "Elon Musk"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "nft",
                        "twitter"
                    ]
                },
                "twitter_user_name": {
                    "type": "string",
                    "example": "elonmusk"
                }
            }
        },
        "api.DuneCategoryFieldRequest": {
            "type": "object",
            "required": [
                "key",
                "type"
            ],
            "properties": {
                "aggregate_sum": {
                    "type": "boolean",
                    "example": true
                },
                "key": {
                    "type": "string",
                    "example": "total_value_locked"
                },
                "note": {
                    "type": "string",
                    "example": "Total Value Locked in USD"
                },
                "required": {
                    "type": "boolean",
                    "example": true
                },
                "type": {
                    "type": "string",
                    "example": "float"
                }
            }
        },
        "api.DuneCategoryRequest": {
            "type": "object",
            "required": [
                "fields",
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "DeFi protocol metrics and data"
                },
                "fields": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.DuneCategoryFieldRequest"
                    }
                },
                "name": {
                    "type": "string",
                    "example": "defi-metrics"
                }
            }
        },
        "api.DuneFetchAllResponse": {
            "type": "object",
            "properties": {
                "duration": {
                    "type": "string",
                    "example": "2.5s"
                },
                "message": {
                    "type": "string",
                    "example": "Dune data fetch triggered successfully"
                },
                "query_count": {
                    "type": "integer",
                    "example": 5
                },
                "status": {
                    "type": "string",
                    "example": "success"
                },
                "triggered_at": {
                    "type": "integer",
                    "example": 1672531200
                },
                "triggered_by": {
                    "type": "string",
                    "example": "manual"
                }
            }
        },
        "api.ExcludedTwitterUserRequest": {
            "type": "object",
            "required": [
                "twitter_user_name"
            ],
            "properties": {
                "twitter_user_name": {
                    "type": "string"
                }
            }
        },
        "api.GetTradeTokenInfoResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {
                    "type": "object",
                    "properties": {
                        "list": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.TradeTokenInfo"
                            }
                        },
                        "page": {
                            "type": "integer"
                        },
                        "page_size": {
                            "type": "integer"
                        },
                        "total_count": {
                            "type": "integer"
                        }
                    }
                },
                "msg": {
                    "type": "string"
                }
            }
        },
        "api.HideListRequest": {
            "type": "object",
            "properties": {
                "usernames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "api.NoticeResponse": {
            "type": "object",
            "properties": {
                "is_business_data": {
                    "type": "boolean"
                },
                "is_ecosystem_partnership": {
                    "type": "boolean"
                },
                "is_industry_event": {
                    "type": "boolean"
                },
                "is_others": {
                    "type": "boolean"
                },
                "is_product_update": {
                    "type": "boolean"
                },
                "is_profit_opportunity": {
                    "type": "boolean"
                }
            }
        },
        "api.TokenDetailsResponse": {
            "type": "object",
            "properties": {
                "chain_id": {
                    "type": "string"
                },
                "holder_count": {
                    "type": "integer"
                },
                "logo_url": {
                    "type": "string"
                },
                "market_cap_usd": {
                    "type": "number"
                },
                "name": {
                    "type": "string"
                },
                "pair_created_at": {
                    "type": "integer"
                },
                "price_usd": {
                    "type": "number"
                },
                "source": {
                    "description": "Added source field",
                    "type": "string"
                },
                "symbol": {
                    "type": "string"
                },
                "twitter_url": {
                    "type": "string"
                }
            }
        },
        "api.TradeTokenInfo": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "ai_report": {
                    "type": "string"
                },
                "banner_url": {
                    "type": "string"
                },
                "chain_id": {
                    "type": "integer"
                },
                "coingecko_url": {
                    "type": "string"
                },
                "coinmarketcap_url": {
                    "type": "string"
                },
                "creation_date": {
                    "type": "string"
                },
                "creator_x_username": {
                    "type": "string"
                },
                "decimals": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "discord_url": {
                    "type": "string"
                },
                "followers_count": {
                    "type": "integer"
                },
                "github_url": {
                    "type": "string"
                },
                "influencers_count": {
                    "type": "integer"
                },
                "instagram_username": {
                    "type": "string"
                },
                "is_verified": {
                    "type": "boolean"
                },
                "is_watched": {
                    "type": "boolean"
                },
                "logo_url": {
                    "type": "string"
                },
                "market_cap": {
                    "type": "string"
                },
                "medium_url": {
                    "type": "string"
                },
                "mobile_banner_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "price_change_in_1hours": {
                    "type": "string"
                },
                "price_change_in_24hours": {
                    "type": "string"
                },
                "price_change_in_6hours": {
                    "type": "string"
                },
                "price_in_usd": {
                    "type": "string"
                },
                "profile": {
                    "type": "string"
                },
                "project_url": {
                    "type": "string"
                },
                "projects_count": {
                    "type": "integer"
                },
                "rank": {
                    "type": "integer"
                },
                "reddit_url": {
                    "type": "string"
                },
                "research_report": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                },
                "symbol": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "color": {
                                "type": "string"
                            },
                            "name": {
                                "type": "string"
                            },
                            "rank": {
                                "type": "integer"
                            },
                            "type": {
                                "type": "integer"
                            }
                        }
                    }
                },
                "telegram_url": {
                    "type": "string"
                },
                "tiktok_url": {
                    "type": "string"
                },
                "top_20_followers": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "avatar": {
                                "type": "string"
                            },
                            "name": {
                                "type": "string"
                            },
                            "username": {
                                "type": "string"
                            }
                        }
                    }
                },
                "total_buy_count_24hours": {
                    "type": "string"
                },
                "total_buyer_count_24hours": {
                    "type": "string"
                },
                "total_liquidity": {
                    "type": "string"
                },
                "total_makers_count_24hours": {
                    "type": "string"
                },
                "total_sell_count_24hours": {
                    "type": "string"
                },
                "total_seller_count_24hours": {
                    "type": "string"
                },
                "total_supply": {
                    "type": "string"
                },
                "total_tx_count_24hours": {
                    "type": "string"
                },
                "total_volume_in_1hours": {
                    "type": "string"
                },
                "total_volume_in_24hours": {
                    "type": "string"
                },
                "total_volume_in_6hours": {
                    "type": "string"
                },
                "twitter_score": {
                    "type": "string"
                },
                "twitter_user_id": {
                    "type": "string"
                },
                "twitter_username": {
                    "type": "string"
                },
                "type": {
                    "type": "integer"
                },
                "venture_capitals_count": {
                    "type": "integer"
                },
                "warpcast_url": {
                    "type": "string"
                }
            }
        },
        "api.TweetInfo": {
            "type": "object",
            "properties": {
                "article_cover_url": {
                    "type": "string"
                },
                "article_preview_text": {
                    "type": "string"
                },
                "article_title": {
                    "type": "string"
                },
                "bookmark_count": {
                    "type": "integer"
                },
                "bullet_points": {},
                "collection_tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "content_type": {
                    "description": "\"tweet\" or \"article\"",
                    "type": "string"
                },
                "favorite_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "notices": {
                    "$ref": "#/definitions/api.NoticeResponse"
                },
                "published_at": {
                    "type": "integer"
                },
                "reply_count": {
                    "type": "integer"
                },
                "retweet_count": {
                    "type": "integer"
                },
                "source_list_type": {
                    "description": "\"KOLs\" or \"Projects\"",
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "text": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api.UserResponse"
                },
                "views_count": {
                    "type": "integer"
                }
            }
        },
        "api.TweetResponse": {
            "type": "object",
            "properties": {
                "article_cover_url": {
                    "type": "string"
                },
                "article_preview_text": {
                    "type": "string"
                },
                "article_title": {
                    "type": "string"
                },
                "bookmark_count": {
                    "type": "integer"
                },
                "bullet_points": {},
                "collection_tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "content_type": {
                    "description": "\"tweet\" or \"article\"",
                    "type": "string"
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.CAResponse"
                    }
                },
                "favorite_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "images": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "notices": {
                    "$ref": "#/definitions/api.NoticeResponse"
                },
                "published_at": {
                    "type": "integer"
                },
                "reply_count": {
                    "type": "integer"
                },
                "retweet_count": {
                    "type": "integer"
                },
                "source_list_type": {
                    "description": "\"KOLs\" or \"Projects\"",
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "text": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/api.UserResponse"
                },
                "views_count": {
                    "type": "integer"
                }
            }
        },
        "api.TwitterUserInfo": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "profile_image_url": {
                    "type": "string"
                },
                "screen_name": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "api.UserAnnouncementStatsResponse": {
            "type": "object",
            "properties": {
                "business_data_count": {
                    "type": "integer"
                },
                "days_since_last_tweet": {
                    "type": "integer"
                },
                "ecosystem_partnership_count": {
                    "type": "integer"
                },
                "industry_events_count": {
                    "type": "integer"
                },
                "others_count": {
                    "type": "integer"
                },
                "product_updates_count": {
                    "type": "integer"
                },
                "profit_opportunity_count": {
                    "type": "integer"
                },
                "total_announcements_count": {
                    "type": "integer"
                },
                "twitter_user": {
                    "$ref": "#/definitions/api.TwitterUserInfo"
                }
            }
        },
        "api.UserResponse": {
            "type": "object",
            "properties": {
                "followers_count": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "is_verified": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "profile_image_url": {
                    "type": "string"
                },
                "screen_name": {
                    "type": "string"
                }
            }
        },
        "db.CategoryQueryMapping": {
            "type": "object",
            "required": [
                "category_id",
                "dune_query_id"
            ],
            "properties": {
                "category_id": {
                    "type": "integer",
                    "example": 1
                },
                "dune_query_id": {
                    "type": "string",
                    "example": "3234567"
                }
            }
        },
        "db.DuneBindingResponse": {
            "type": "object",
            "properties": {
                "category_queries": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.CategoryQueryMapping"
                    }
                },
                "chain_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "project_logo": {
                    "type": "string"
                },
                "project_name": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "twitter_user_name": {
                    "type": "string"
                }
            }
        },
        "db.DuneCategory": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "fields": {
                    "description": "Relationships",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.DuneCategoryField"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "db.DuneCategoryField": {
            "type": "object",
            "properties": {
                "aggregate_sum": {
                    "description": "Controls whether to use sum aggregation for comparison",
                    "type": "boolean"
                },
                "category_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "key": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "required": {
                    "type": "boolean"
                },
                "type": {
                    "description": "int, string, float, etc.",
                    "type": "string"
                }
            }
        },
        "db.DuneTwitterBinding": {
            "type": "object",
            "properties": {
                "categories": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.DuneCategory"
                    }
                },
                "chain_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "dune_query_id": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "project_logo": {
                    "type": "string"
                },
                "project_name": {
                    "type": "string"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "twitter_user_name": {
                    "type": "string"
                }
            }
        },
        "db.ExcludedTwitterUser": {
            "type": "object",
            "properties": {
                "excluded_at": {
                    "type": "string"
                },
                "excluded_by": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "reason": {
                    "type": "string"
                },
                "twitter_user_name": {
                    "type": "string"
                }
            }
        },
        "db.JSONB": {
            "type": "object",
            "additionalProperties": true
        },
        "services.AllProjectsDailyFieldSumResponse": {
            "type": "object",
            "properties": {
                "bindings_count": {
                    "description": "Number of bindings (total for all projects, category count for specific category)",
                    "type": "integer"
                },
                "category_description": {
                    "description": "Category description (empty for all projects)",
                    "type": "string"
                },
                "category_id": {
                    "description": "Category ID (0 for all projects)",
                    "type": "integer"
                },
                "category_name": {
                    "description": "Category name (empty for all projects)",
                    "type": "string"
                },
                "daily_data": {
                    "description": "Daily aggregated data (when field is specified)",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/services.DailyFieldSumData"
                    }
                },
                "daily_fields_data": {
                    "description": "All fields daily data when field is not specified (time in outer layer)",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/services.DailyFieldsData"
                    }
                },
                "days": {
                    "description": "Number of days requested (0 for all)",
                    "type": "integer"
                },
                "field": {
                    "description": "Single field name when field is specified",
                    "type": "string"
                },
                "period": {
                    "description": "Analysis period",
                    "type": "string"
                }
            }
        },
        "services.AllProjectsFieldSumComparisonResponse": {
            "type": "object",
            "properties": {
                "analysis_period": {
                    "type": "string"
                },
                "change_rate": {
                    "description": "Single field change rate when field is specified",
                    "type": "number"
                },
                "comparison_period": {
                    "type": "string"
                },
                "current_sum": {
                    "description": "Single field sum when field is specified",
                    "type": "number"
                },
                "days": {
                    "type": "integer"
                },
                "field": {
                    "description": "Single field name when field is specified",
                    "type": "string"
                },
                "fields_data": {
                    "description": "All fields data when field is not specified",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/services.FieldSumData"
                    }
                },
                "previous_sum": {
                    "description": "Single field sum when field is specified",
                    "type": "number"
                },
                "total_bindings_count": {
                    "type": "integer"
                }
            }
        },
        "services.CategoryDailyFieldSumResponse": {
            "type": "object",
            "properties": {
                "bindings_count": {
                    "description": "Number of bindings (total for all projects, category count for specific category)",
                    "type": "integer"
                },
                "category_description": {
                    "description": "Category description (empty for all projects)",
                    "type": "string"
                },
                "category_id": {
                    "description": "Category ID (0 for all projects)",
                    "type": "integer"
                },
                "category_name": {
                    "description": "Category name (empty for all projects)",
                    "type": "string"
                },
                "daily_data": {
                    "description": "Daily aggregated data (when field is specified)",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/services.DailyFieldSumData"
                    }
                },
                "daily_fields_data": {
                    "description": "All fields daily data when field is not specified (time in outer layer)",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/services.DailyFieldsData"
                    }
                },
                "days": {
                    "description": "Number of days requested (0 for all)",
                    "type": "integer"
                },
                "field": {
                    "description": "Single field name when field is specified",
                    "type": "string"
                },
                "period": {
                    "description": "Analysis period",
                    "type": "string"
                }
            }
        },
        "services.CategoryFieldSumComparisonResponse": {
            "type": "object",
            "properties": {
                "analysis_period": {
                    "type": "string"
                },
                "bindings_count": {
                    "type": "integer"
                },
                "category_description": {
                    "type": "string"
                },
                "category_id": {
                    "type": "integer"
                },
                "category_name": {
                    "type": "string"
                },
                "change_rate": {
                    "description": "Single field change rate when field is specified",
                    "type": "number"
                },
                "comparison_period": {
                    "type": "string"
                },
                "current_sum": {
                    "description": "Single field sum when field is specified",
                    "type": "number"
                },
                "days": {
                    "type": "integer"
                },
                "field": {
                    "description": "Single field name when field is specified",
                    "type": "string"
                },
                "fields_data": {
                    "description": "All fields data when field is not specified",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/services.FieldSumData"
                    }
                },
                "previous_sum": {
                    "description": "Single field sum when field is specified",
                    "type": "number"
                }
            }
        },
        "services.DailyFieldSumData": {
            "type": "object",
            "properties": {
                "date": {
                    "description": "Date in YYYY-MM-DD format",
                    "type": "string"
                },
                "sum": {
                    "description": "Sum value for the day",
                    "type": "number"
                }
            }
        },
        "services.DailyFieldsData": {
            "type": "object",
            "properties": {
                "date": {
                    "description": "Date in YYYY-MM-DD format",
                    "type": "string"
                },
                "fields_data": {
                    "description": "All fields sum data for this date",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number",
                        "format": "float64"
                    }
                }
            }
        },
        "services.DuneQueryResultResponse": {
            "type": "object",
            "properties": {
                "comparison_data": {
                    "description": "Historical period data for comparison",
                    "allOf": [
                        {
                            "$ref": "#/definitions/db.JSONB"
                        }
                    ]
                },
                "contract_interaction": {
                    "type": "integer"
                },
                "contract_interaction_change_rate": {
                    "description": "Contract interaction change rate compared to previous period",
                    "type": "number"
                },
                "data": {
                    "description": "Dynamic fields data",
                    "allOf": [
                        {
                            "$ref": "#/definitions/db.JSONB"
                        }
                    ]
                },
                "data_change_rates": {
                    "description": "Dynamic fields change rates",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number",
                        "format": "float64"
                    }
                },
                "query_date": {
                    "type": "string"
                },
                "users": {
                    "type": "integer"
                },
                "users_change_rate": {
                    "description": "Users change rate compared to previous period",
                    "type": "number"
                }
            }
        },
        "services.FieldSumData": {
            "type": "object",
            "properties": {
                "change_rate": {
                    "type": "number"
                },
                "current_sum": {
                    "type": "number"
                },
                "previous_sum": {
                    "type": "number"
                }
            }
        },
        "services.ProjectDataResponse": {
            "type": "object",
            "properties": {
                "chain_ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "contract_addresses": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "dune_query_id": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "project_logo": {
                    "type": "string"
                },
                "project_name": {
                    "type": "string"
                },
                "query_result": {
                    "description": "New single result field",
                    "allOf": [
                        {
                            "$ref": "#/definitions/services.DuneQueryResultResponse"
                        }
                    ]
                },
                "query_results": {
                    "description": "For backward compatibility",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/services.DuneQueryResultResponse"
                    }
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "twitter_user_name": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api",
	Schemes:          []string{},
	Title:            "Real-Time CA Service API",
	Description:      "API for the Real-Time Contract Address service",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
