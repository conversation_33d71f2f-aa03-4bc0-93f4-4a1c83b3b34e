package db

import (
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
)

// MigrateDatabase runs database migrations using GORM's AutoMigrate
func MigrateDatabase(db *Database) error {
	log.Info().Msg("Running database migrations with GORM...")

	// Run migrations
	start := time.Now()

	// Enable PostgreSQL extensions
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";").Error; err != nil {
		return fmt.Errorf("failed to create uuid-ossp extension: %w", err)
	}

	// Enable citext extension for case-insensitive text fields
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"citext\";").Error; err != nil {
		return fmt.Errorf("failed to create citext extension: %w", err)
	}

	// Auto migrate all models
	if err := db.AutoMigrate(
		&TwitterUser{},
		&Tweet{},
		&RecognizedCA{},
		&TokenDetails{},
		&Tag{},
		&RecognizedCATag{},
		&TweetTag{},
		&CollectionTag{},
		&DuneTwitterBinding{},
		&DuneQueryResult{},
		&DuneCategory{},
		&DuneCategoryField{},
		&DuneBindingCategory{},
		&ExcludedTwitterUser{},
	); err != nil {
		return fmt.Errorf("failed to run auto migrations: %w", err)
	}

	log.Info().
		Dur("duration", time.Since(start)).
		Msg("Database migrations completed successfully")

	return nil
}
