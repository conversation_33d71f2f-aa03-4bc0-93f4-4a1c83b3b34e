package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"real-time-ca-service/internal/config"
	"strconv"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"real-time-ca-service/internal/db"

	"github.com/spf13/cast"
)

// DuneService handles Dune query management and data fetching
type DuneService struct {
	db             *db.Database
	config         DuneConfig
	notifyTelegram *TelegramService
}

// DuneConfig holds configuration for Dune service
type DuneConfig struct {
	APIKey     string
	BaseURL    string
	Timeout    time.Duration
	MaxRetries int
	RetryDelay time.Duration
}

// NewDuneService creates a new DuneService
func NewDuneService(conf config.DuneConfig, database *db.Database) *DuneService {
	return &DuneService{
		db: database,
		config: DuneConfig{
			APIKey:     conf.APIKey,
			BaseURL:    conf.BaseURL,
			Timeout:    conf.Timeout,
			MaxRetries: conf.MaxRetries,
			RetryDelay: conf.<PERSON>tryDelay,
		},
	}
}

// SetNotifyTelegram sets the secondary Telegram notifier service used for Dune updates
func (s *DuneService) SetNotifyTelegram(telegram *TelegramService) {
	s.notifyTelegram = telegram
}

// GetDB returns the database instance for external access
func (s *DuneService) GetDB() *db.Database {
	return s.db
}

// === Category Management Methods ===

// CreateCategory creates a new dune category with fields
func (s *DuneService) CreateCategory(name, description string, fields []db.DuneCategoryField) (*db.DuneCategory, error) {
	// Check if category name already exists
	var existing db.DuneCategory
	err := s.db.DB.Where("name = ?", name).First(&existing).Error
	if err == nil {
		return nil, fmt.Errorf("category with name '%s' already exists", name)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("error checking existing category: %w", err)
	}

	// Create category with fields in a transaction
	var category *db.DuneCategory
	err = s.db.Transaction(func(tx *gorm.DB) error {
		category = &db.DuneCategory{
			Name:        name,
			Description: description,
		}

		if err := tx.Create(category).Error; err != nil {
			return fmt.Errorf("failed to create category: %w", err)
		}

		// Create fields
		for i := range fields {
			fields[i].CategoryID = category.ID
			if err = tx.Create(&fields[i]).Error; err != nil {
				return fmt.Errorf("failed to create field '%s': %w", fields[i].Key, err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Load the category with fields
	return s.GetCategoryByID(category.ID)
}

// GetCategoryByID retrieves a category by ID with its fields
func (s *DuneService) GetCategoryByID(id int64) (*db.DuneCategory, error) {
	var category db.DuneCategory
	err := s.db.DB.Preload("Fields").First(&category, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("category with id %d not found", id)
		}
		return nil, fmt.Errorf("error retrieving category: %w", err)
	}
	return &category, nil
}

// GetCategoryByName retrieves a category by name with its fields
func (s *DuneService) GetCategoryByName(name string) (*db.DuneCategory, error) {
	var category db.DuneCategory
	err := s.db.DB.Preload("Fields").Where("name = ?", name).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("category with name '%s' not found", name)
		}
		return nil, fmt.Errorf("error retrieving category: %w", err)
	}
	return &category, nil
}

// GetAllCategories retrieves all categories with their fields
func (s *DuneService) GetAllCategories() ([]*db.DuneCategory, error) {
	var categories []*db.DuneCategory
	err := s.db.DB.Preload("Fields").Find(&categories).Error
	if err != nil {
		return nil, fmt.Errorf("error retrieving categories: %w", err)
	}
	return categories, nil
}

// UpdateCategory updates an existing category
func (s *DuneService) UpdateCategory(id int64, name, description string, fields []db.DuneCategoryField) (*db.DuneCategory, error) {
	// Check if category exists
	var existing db.DuneCategory
	err := s.db.DB.First(&existing, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("category with id %d not found", id)
		}
		return nil, fmt.Errorf("error checking existing category: %w", err)
	}

	// Check if name conflicts with another category
	if name != existing.Name {
		var nameConflict db.DuneCategory
		err = s.db.DB.Where("name = ? AND id != ?", name, id).First(&nameConflict).Error
		if err == nil {
			return nil, fmt.Errorf("another category with name '%s' already exists", name)
		}
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("error checking name conflict: %w", err)
		}
	}

	// Update category and fields in transaction
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// Update category
		if err = tx.Model(&existing).Updates(map[string]interface{}{
			"name":        name,
			"description": description,
		}).Error; err != nil {
			return fmt.Errorf("failed to update category: %w", err)
		}

		// Delete existing fields
		if err = tx.Where("category_id = ?", id).Delete(&db.DuneCategoryField{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing fields: %w", err)
		}

		// Create new fields
		for i := range fields {
			fields[i].CategoryID = id
			fields[i].ID = 0 // Reset ID to create new record
		}

		if err = tx.CreateInBatches(&fields, len(fields)).Error; err != nil {
			return fmt.Errorf("failed to create field: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return s.GetCategoryByID(id)
}

// DeleteCategory deletes a category and all its associations
func (s *DuneService) DeleteCategory(id int64) error {
	// Check if category exists
	var category db.DuneCategory
	err := s.db.DB.First(&category, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("category with id %d not found", id)
		}
		return fmt.Errorf("error checking existing category: %w", err)
	}

	// Delete category (cascade will handle fields and binding associations)
	err = s.db.DB.Delete(&category).Error
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	log.Info().Int64("category_id", id).Str("category_name", category.Name).Msg("Category deleted successfully")
	return nil
}

// === Binding-Category Association Methods ===

// AssociateBindingWithCategories associates a binding with multiple categories
func (s *DuneService) AssociateBindingWithCategories(bindingID int64, categoryIDs []int64) error {
	// Verify binding exists
	var binding db.DuneTwitterBinding
	err := s.db.DB.First(&binding, bindingID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("binding with id %d not found", bindingID)
		}
		return fmt.Errorf("error checking binding: %w", err)
	}

	// Verify all categories exist
	var categories []db.DuneCategory
	err = s.db.DB.Where("id IN ?", categoryIDs).Find(&categories).Error
	if err != nil {
		return fmt.Errorf("error checking categories: %w", err)
	}
	if len(categories) != len(categoryIDs) {
		return fmt.Errorf("some categories not found")
	}

	// Replace associations in transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete existing associations
		if err := tx.Where("binding_id = ?", bindingID).Delete(&db.DuneBindingCategory{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing associations: %w", err)
		}

		// Create new associations
		for _, categoryID := range categoryIDs {
			association := db.DuneBindingCategory{
				BindingID:  bindingID,
				CategoryID: categoryID,
			}
			if err := tx.Create(&association).Error; err != nil {
				return fmt.Errorf("failed to create association with category %d: %w", categoryID, err)
			}
		}

		return nil
	})
}

// GetBindingWithCategories retrieves a binding with its associated categories and fields
func (s *DuneService) GetBindingWithCategories(id int64) (*db.DuneTwitterBinding, error) {
	var binding db.DuneTwitterBinding
	err := s.db.DB.Preload("Categories.Fields").First(&binding, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("binding with id %d not found", id)
		}
		return nil, fmt.Errorf("error retrieving binding: %w", err)
	}
	return &binding, nil
}

// GetBindingsByCategory retrieves all bindings associated with a category
func (s *DuneService) GetBindingsByCategory(categoryID int64) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	err := s.db.DB.Joins("JOIN dune_binding_categories dbc ON dbc.binding_id = dune_twitter_bindings.id").
		Where("dbc.category_id = ?", categoryID).
		Preload("Categories.Fields").
		Find(&bindings).Error
	if err != nil {
		return nil, fmt.Errorf("error retrieving bindings by category: %w", err)
	}

	// Populate tags from CollectionTag table if empty using batch query
	_ = s.populateTagsForBindings(bindings)

	return bindings, nil
}

// GetFieldsForBinding gets all fields for a binding based on its associated categories
func (s *DuneService) GetFieldsForBinding(bindingID int64) ([]db.DuneCategoryField, error) {
	var fields []db.DuneCategoryField

	// Get all fields from categories associated with this binding
	err := s.db.DB.Joins("JOIN dune_categories dc ON dc.id = dune_category_fields.category_id").
		Joins("JOIN dune_binding_categories dbc ON dbc.category_id = dc.id").
		Where("dbc.binding_id = ?", bindingID).
		Find(&fields).Error

	if err != nil {
		return nil, fmt.Errorf("error retrieving fields for binding: %w", err)
	}

	return fields, nil
}

// GetCategoriesForBinding retrieves all categories associated with a binding
func (s *DuneService) GetCategoriesForBinding(bindingID int64) ([]db.DuneCategory, error) {
	var categories []db.DuneCategory

	// Get all categories associated with this binding
	err := s.db.DB.Joins("JOIN dune_binding_categories dbc ON dbc.category_id = dune_categories.id").
		Where("dbc.binding_id = ?", bindingID).
		Preload("Fields").
		Find(&categories).Error

	if err != nil {
		return nil, fmt.Errorf("error retrieving categories for binding: %w", err)
	}

	return categories, nil
}

// AssociateCategoriesWithBinding is an alias for AssociateBindingWithCategories for API consistency
func (s *DuneService) AssociateCategoriesWithBinding(bindingID int64, categoryIDs []int64) error {
	return s.AssociateBindingWithCategories(bindingID, categoryIDs)
}

// === Helper Methods for Dynamic Data Parsing ===

// getBindingsByQueryID gets all bindings that use a specific query ID
func (s *DuneService) getBindingsByQueryID(queryID string) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	err := s.db.DB.Where("dune_query_id = ?", queryID).
		Preload("Categories.Fields").
		Find(&bindings).Error
	if err != nil {
		return nil, fmt.Errorf("error retrieving bindings for query ID: %w", err)
	}
	return bindings, nil
}

// getCombinedFieldsForQueryID gets the combined set of all fields for bindings using a query ID
func (s *DuneService) getCombinedFieldsForQueryID(queryID string) ([]db.DuneCategoryField, error) {
	// Get all unique fields across all bindings for this query
	var fields []db.DuneCategoryField

	err := s.db.DB.Distinct("dune_category_fields.*").
		Joins("JOIN dune_categories dc ON dc.id = dune_category_fields.category_id").
		Joins("JOIN dune_binding_categories dbc ON dbc.category_id = dc.id").
		Joins("JOIN dune_twitter_bindings dtb ON dtb.id = dbc.binding_id").
		Where("dtb.dune_query_id = ?", queryID).
		Find(&fields).Error

	if err != nil {
		return nil, fmt.Errorf("error getting combined fields for query ID: %w", err)
	}

	return fields, nil
}

// LegacyData represents the legacy ContractInteraction and Users fields
type LegacyData struct {
	ContractInteraction int
	Users               int
}

// parseDuneRowData parses a Dune API row based on field definitions
func (s *DuneService) parseDuneRowData(row map[string]interface{}, fields []db.DuneCategoryField) (db.JSONB, LegacyData, error) {
	// Initialize dynamic data map
	dynamicData := make(map[string]interface{})

	// Extract legacy fields for backward compatibility
	var contractInteraction, users int
	if val, ok := row["Contract Interaction"]; ok {
		if intVal, err := s.convertFieldValue(val, "int"); err == nil {
			contractInteraction = intVal.(int)
		}
	}
	if val, ok := row["Users"]; ok {
		if intVal, err := s.convertFieldValue(val, "int"); err == nil {
			users = intVal.(int)
		}
	}

	legacyData := LegacyData{
		ContractInteraction: contractInteraction,
		Users:               users,
	}

	// Parse fields based on category field definitions
	for _, field := range fields {
		if value, exists := row[field.Key]; exists {
			// Validate field value is not null/empty if required
			if field.Required && (value == nil || value == "") {
				return nil, legacyData, fmt.Errorf("required field '%s' has empty/null value", field.Key)
			}

			// Try to convert value based on field type
			convertedValue, err := s.convertFieldValue(value, field.Type)
			if err != nil {
				log.Error().Err(err).Str("field_key", field.Key).Str("field_type", field.Type).
					Interface("raw_value", value).Msg("Failed to convert field value with expected type")
				// For strict type checking, return error instead of using raw value
				return nil, legacyData, fmt.Errorf("field '%s' failed type conversion: expected %s but got %T with value %v",
					field.Key, field.Type, value, value)
			}
			dynamicData[field.Key] = convertedValue
		} else if field.Required {
			return nil, legacyData, fmt.Errorf("required field '%s' not found in row data", field.Key)
		}
	}

	return dynamicData, legacyData, nil
}

// calculateDynamicFieldChangeRates calculates change rates for all dynamic fields
func (s *DuneService) calculateDynamicFieldChangeRates(currentData, comparisonData db.JSONB, fields []db.DuneCategoryField) map[string]*float64 {
	changeRates := make(map[string]*float64)

	if currentData == nil || comparisonData == nil {
		return changeRates
	}

	// Create a map of field keys to field types for type-aware calculation
	fieldTypeMap := make(map[string]string)
	for _, field := range fields {
		fieldTypeMap[field.Key] = field.Type
	}

	// Calculate change rates for each dynamic field
	for key, currentValue := range currentData {
		comparisonValue, hasComparison := comparisonData[key]
		if !hasComparison || comparisonValue == nil || currentValue == nil {
			continue
		}

		// Convert values to float64 for calculation
		currentFloat, err1 := s.convertToFloat64(currentValue)
		comparisonFloat, err2 := s.convertToFloat64(comparisonValue)

		if err1 != nil || err2 != nil {
			// Cannot calculate change rate, skip this field
			continue
		}

		// Handle special case when comparison value is 0
		var changeRate float64
		if comparisonFloat == 0 {
			// When base value is 0, calculate as percentage of current value
			// If current is also 0, change rate is 0; otherwise it's infinite growth
			if currentFloat == 0 {
				changeRate = 0
			} else {
				// Represent infinite growth as 100% per unit of current value
				changeRate = currentFloat * 100
			}
		} else {
			// Calculate percentage change rate using absolute value difference
			// Examples: 100->-100 = -200%, -100->100 = +200%, -100->-300 = -200%
			changeRate = (currentFloat - comparisonFloat) / math.Abs(comparisonFloat) * 100
		}

		changeRates[key] = &changeRate
	}

	return changeRates
}

// generateComparisonData generates comparison data that reflects the actual values used for comparison
// (aggregated for sum fields, latest for non-aggregate fields)
func (s *DuneService) generateComparisonData(comparisonResults []*db.DuneQueryResult, comparisonAggregatedData db.JSONB, fields []db.DuneCategoryField) db.JSONB {
	if len(comparisonResults) == 0 {
		return db.JSONB{}
	}

	comparisonData := make(map[string]interface{})
	latestComparison := comparisonResults[len(comparisonResults)-1]

	// Process each field based on its aggregation configuration
	for _, field := range fields {
		key := field.Key
		useAggregateSum := field.AggregateSum != nil && *field.AggregateSum

		if useAggregateSum {
			// Use aggregated data for sum fields
			if value, exists := comparisonAggregatedData[key]; exists {
				comparisonData[key] = value
			}
		} else {
			// Use latest single record for non-aggregate fields
			if latestComparison.Data != nil {
				if value, exists := latestComparison.Data[key]; exists {
					comparisonData[key] = value
				}
			}
		}
	}

	return db.JSONB(comparisonData)
}

// calculateDynamicFieldChangeRatesWithLatestComparison calculates change rates for dynamic fields
// supporting both sum aggregation and latest value comparison based on field configuration
func (s *DuneService) calculateDynamicFieldChangeRatesWithLatestComparison(
	currentResults, comparisonResults []*db.DuneQueryResult,
	currentAggregatedData, comparisonAggregatedData db.JSONB,
	fields []db.DuneCategoryField,
) map[string]*float64 {
	changeRates := make(map[string]*float64)

	if len(currentResults) == 0 || len(comparisonResults) == 0 {
		return changeRates
	}

	// Create field configuration maps
	fieldAggregateSumMap := make(map[string]*bool)
	for _, field := range fields {
		fieldAggregateSumMap[field.Key] = field.AggregateSum
	}

	// Get latest results for non-aggregate fields
	latestCurrent := currentResults[len(currentResults)-1]
	latestComparison := comparisonResults[len(comparisonResults)-1]

	// Process each field based on its aggregation configuration
	for _, field := range fields {
		key := field.Key
		useAggregateSum := field.AggregateSum

		var currentValue, comparisonValue interface{}
		var hasCurrentValue, hasComparisonValue bool

		if *useAggregateSum {
			// Use aggregated data for sum fields
			currentValue, hasCurrentValue = currentAggregatedData[key]
			comparisonValue, hasComparisonValue = comparisonAggregatedData[key]
		} else {
			// Use latest single record for non-aggregate fields
			if latestCurrent.Data != nil {
				currentValue, hasCurrentValue = latestCurrent.Data[key]
			}
			if latestComparison.Data != nil {
				comparisonValue, hasComparisonValue = latestComparison.Data[key]
			}
		}

		if !hasCurrentValue || !hasComparisonValue || currentValue == nil || comparisonValue == nil {
			continue
		}

		// Convert values to float64 for calculation
		currentFloat, err1 := s.convertToFloat64(currentValue)
		comparisonFloat, err2 := s.convertToFloat64(comparisonValue)

		if err1 != nil || err2 != nil {
			// Cannot calculate change rate, skip this field
			continue
		}

		// Handle special case when comparison value is 0
		var changeRate float64
		if comparisonFloat == 0 {
			// When base value is 0, calculate as percentage of current value
			// If current is also 0, change rate is 0; otherwise it's infinite growth
			if currentFloat == 0 {
				changeRate = 0
			} else {
				// Represent infinite growth as 100% per unit of current value
				changeRate = currentFloat * 100
			}
		} else {
			// Calculate percentage change rate using absolute value difference
			// Examples: 100->-100 = -200%, -100->100 = +200%, -100->-300 = -200%
			changeRate = (currentFloat - comparisonFloat) / math.Abs(comparisonFloat) * 100
		}

		changeRates[key] = &changeRate
	}

	return changeRates
}

// filterResponseByField filters the DuneQueryResultResponse to only include the specified field
// in both Data and DataChangeRates
func (s *DuneService) filterResponseByField(response *DuneQueryResultResponse, field string) {
	if response == nil || field == "" {
		return
	}

	// Helper to normalize keys (same as in calculateDynamicFieldChangeRates)
	normalize := func(s string) string {
		s = strings.TrimSpace(s)
		s = strings.ToLower(s)
		s = strings.ReplaceAll(s, " ", "_")
		s = strings.ReplaceAll(s, "-", "_")
		return s
	}

	targetField := normalize(field)

	// Filter Data to only include the specified field
	if response.Data != nil {
		filteredData := make(db.JSONB)
		for key, value := range response.Data {
			if normalize(key) == targetField {
				filteredData[key] = value
				break
			}
		}
		response.Data = filteredData
	}

	// Filter DataChangeRates to only include the specified field
	if response.DataChangeRates != nil {
		filteredChangeRates := make(map[string]*float64)
		for key, value := range response.DataChangeRates {
			if normalize(key) == targetField {
				filteredChangeRates[key] = value
				break
			}
		}
		response.DataChangeRates = filteredChangeRates
	}

	// Filter ComparisonData to only include the specified field
	if response.ComparisonData != nil {
		filteredComparisonData := make(db.JSONB)
		for key, value := range response.ComparisonData {
			if normalize(key) == targetField {
				filteredComparisonData[key] = value
				break
			}
		}
		response.ComparisonData = filteredComparisonData
	}
}

// convertToFloat64 safely converts interface{} to float64 for change rate calculation
func (s *DuneService) convertToFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		if parsed, err := strconv.ParseFloat(v, 64); err == nil {
			return parsed, nil
		}
		return 0, fmt.Errorf("cannot convert string '%s' to float64", v)
	default:
		return 0, fmt.Errorf("unsupported type for float64 conversion: %T", v)
	}
}

// convertFieldValue converts a value to the specified type
func (s *DuneService) convertFieldValue(value interface{}, fieldType string) (interface{}, error) {
	// Handle nil values
	if value == nil {
		switch fieldType {
		case "int", "integer":
			return 0, nil
		case "float", "double", "decimal":
			return 0.0, nil
		case "string", "text":
			return "", nil
		case "boolean", "bool":
			return false, nil
		default:
			return nil, nil
		}
	}

	switch fieldType {
	case "int", "integer":
		switch v := value.(type) {
		case float64:
			return int(v), nil
		case int:
			return v, nil
		case string:
			if parsed, err := strconv.Atoi(v); err == nil {
				return parsed, nil
			}
			return 0, fmt.Errorf("cannot convert string '%s' to int", v)
		default:
			return 0, fmt.Errorf("unsupported type for int conversion: %T", v)
		}
	case "float", "double", "decimal":
		switch v := value.(type) {
		case float64:
			return v, nil
		case int:
			return float64(v), nil
		case string:
			if parsed, err := strconv.ParseFloat(v, 64); err == nil {
				return parsed, nil
			}
			return 0.0, fmt.Errorf("cannot convert string '%s' to float", v)
		default:
			return 0.0, fmt.Errorf("unsupported type for float conversion: %T", v)
		}
	case "string", "text":
		return fmt.Sprintf("%v", value), nil
	case "boolean", "bool":
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			switch strings.ToLower(v) {
			case "true", "1", "yes", "on":
				return true, nil
			case "false", "0", "no", "off", "":
				return false, nil
			default:
				return false, fmt.Errorf("cannot convert string '%s' to bool", v)
			}
		case float64:
			return v != 0, nil
		case int:
			return v != 0, nil
		default:
			return false, fmt.Errorf("unsupported type for bool conversion: %T", v)
		}
	default:
		// For unknown types, just return the value as-is
		return value, nil
	}
}

// https://api.dune.com/api/v1/query/5399608/results?limit=1000
type DuneQueryResults struct {
	ExecutionId         string    `json:"execution_id"`
	QueryId             int       `json:"query_id"`
	IsExecutionFinished bool      `json:"is_execution_finished"`
	State               string    `json:"state"`
	SubmittedAt         time.Time `json:"submitted_at"`
	ExpiresAt           time.Time `json:"expires_at"`
	ExecutionStartedAt  time.Time `json:"execution_started_at"`
	ExecutionEndedAt    time.Time `json:"execution_ended_at"`
	Result              struct {
		Rows     []map[string]interface{} `json:"rows"`
		Metadata struct {
			ColumnNames         []string `json:"column_names"`
			ColumnTypes         []string `json:"column_types"`
			RowCount            int      `json:"row_count"`
			ResultSetBytes      int      `json:"result_set_bytes"`
			TotalRowCount       int      `json:"total_row_count"`
			TotalResultSetBytes int      `json:"total_result_set_bytes"`
			DatapointCount      int      `json:"datapoint_count"`
			PendingTimeMillis   int      `json:"pending_time_millis"`
			ExecutionTimeMillis int      `json:"execution_time_millis"`
		} `json:"metadata"`
	} `json:"result"`
}

// CaByUserNameResponse represents the response from the CA by username API
type CaByUserNameResponse struct {
	Code int `json:"code"`
	Data struct {
		Data []struct {
			Address string `json:"address"`
			ChainId int    `json:"chain_id"`
			Slug    string `json:"slug"`
		} `json:"data"`
		TotalCount int `json:"total_count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

// getCaByUserName calls the external API to get CA by username
func (s *DuneService) getCaByUserName(userName string) (*CaByUserNameResponse, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	apiURL := fmt.Sprintf("https://api.scattering.io/api/v3/tokens/by_tweet_username?twitter_username=%s", url.QueryEscape(userName))

	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call CA by username API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	var result CaByUserNameResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// getSlugByUserName gets the slug for a Twitter username from the external API
func (s *DuneService) getSlugByUserName(userName string) string {
	response, err := s.getCaByUserName(userName)
	if err != nil {
		log.Warn().Err(err).Str("username", userName).Msg("Failed to get slug from external API, using username as fallback")
		return userName // Fallback to username if API call fails
	}

	// Check if we have any data
	if response.Code == 200 && len(response.Data.Data) > 0 {
		// Return the first slug if available
		if response.Data.Data[0].Slug != "" {
			return response.Data.Data[0].Slug
		}
	}

	// Fallback to username if no slug found
	return userName
}

// CreateBinding creates a new Twitter-Dune query binding
func (s *DuneService) CreateBinding(binding *db.DuneTwitterBinding) error {
	// Check if Dune query ID already exists
	var existingByQuery db.DuneTwitterBinding
	err := s.db.DB.Where("dune_query_id = ?", binding.DuneQueryID).First(&existingByQuery).Error
	if err == nil {
		return fmt.Errorf("dune query ID '%s' is already bound to another Twitter user", binding.DuneQueryID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing Dune query ID: %w", err)
	}

	// Create new binding (allow multiple bindings for same Twitter username)
	if err := s.db.DB.Create(binding).Error; err != nil {
		return fmt.Errorf("failed to create binding: %w", err)
	}

	log.Info().
		Str("dune_query_id", binding.DuneQueryID).
		Str("twitter_user_name", binding.TwitterUserName).
		Msg("Created new Dune Twitter binding")

	return nil
}

// GetBinding retrieves a binding by ID
func (s *DuneService) GetBinding(id int64) (*db.DuneTwitterBinding, error) {
	var binding db.DuneTwitterBinding
	if err := s.db.DB.First(&binding, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}

	// If tags are empty, populate from CollectionTag table
	if len(binding.Tags) == 0 {
		tags, err := s.getTagsFromCollectionTag(binding.TwitterUserName)
		if err != nil {
			log.Warn().Err(err).Str("twitter_username", binding.TwitterUserName).Msg("Failed to get tags from collection_tags")
		} else {
			binding.Tags = tags
		}
	}

	return &binding, nil
}

// convertToBindingResponse converts DuneTwitterBinding to DuneBindingResponse with category_queries
func (s *DuneService) convertToBindingResponse(binding *db.DuneTwitterBinding) (*db.DuneBindingResponse, error) {
	if binding == nil {
		return nil, nil
	}

	// Get categories for this binding
	var bindingWithCategories db.DuneTwitterBinding
	err := s.db.DB.Preload("Categories").First(&bindingWithCategories, binding.ID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to load categories for binding %d: %w", binding.ID, err)
	}

	// Build category_queries array
	categoryQueries := make([]db.CategoryQueryMapping, 0)
	for _, category := range bindingWithCategories.Categories {
		categoryQueries = append(categoryQueries, db.CategoryQueryMapping{
			CategoryID:  category.ID,
			DuneQueryID: binding.DuneQueryID, // Same query ID for all categories this binding belongs to
		})
	}

	response := &db.DuneBindingResponse{
		ID:                binding.ID,
		CategoryQueries:   categoryQueries,
		TwitterUserName:   binding.TwitterUserName,
		ChainIDs:          binding.ChainIDs,
		ProjectName:       binding.ProjectName,
		ProjectLogo:       binding.ProjectLogo,
		ContractAddresses: binding.ContractAddresses,
		Tags:              binding.Tags,
	}

	return response, nil
}

// GetAllBindingsWithCategoryQueries retrieves all bindings with category_queries structure
func (s *DuneService) GetAllBindingsWithCategoryQueries(limit, offset int) ([]*db.DuneBindingResponse, error) {
	bindings, err := s.GetAllBindings(limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*db.DuneBindingResponse, 0, len(bindings))
	for _, binding := range bindings {
		response, err := s.convertToBindingResponse(binding)
		if err != nil {
			return nil, err
		}
		if response != nil {
			responses = append(responses, response)
		}
	}

	return responses, nil
}

// GetBindingWithCategoryQueries retrieves a specific binding with category_queries structure
func (s *DuneService) GetBindingWithCategoryQueries(id int64) (*db.DuneBindingResponse, error) {
	binding, err := s.GetBinding(id)
	if err != nil {
		return nil, err
	}
	if binding == nil {
		return nil, nil
	}

	return s.convertToBindingResponse(binding)
}

// GetBindingByTwitterUser retrieves bindings by Twitter username
func (s *DuneService) GetBindingByTwitterUser(twitterUserName string) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Where("twitter_user_name = ?", twitterUserName).Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get bindings by Twitter user: %w", err)
	}

	// Populate tags from CollectionTag table if empty using batch query
	_ = s.populateTagsForBindings(bindings)

	return bindings, nil
}

func (s *DuneService) GetBindingByID(idStr string) (*db.DuneTwitterBinding, error) {
	var binding db.DuneTwitterBinding
	if err := s.db.DB.First(&binding, idStr).Error; err != nil {
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}

	// Populate tags from CollectionTag table if empty using batch query
	_ = s.populateTagsForBindings([]*db.DuneTwitterBinding{&binding})

	return &binding, nil
}

// GetAllBindings retrieves all bindings with pagination
func (s *DuneService) GetAllBindings(limit, offset int) ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Limit(limit).Offset(offset).Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	// Populate tags from CollectionTag table if empty using batch query
	_ = s.populateTagsForBindings(bindings)

	return bindings, nil
}

// GetAllBindingsWithoutPagination retrieves all bindings without pagination
func (s *DuneService) GetAllBindingsWithoutPagination() ([]*db.DuneTwitterBinding, error) {
	var bindings []*db.DuneTwitterBinding
	if err := s.db.DB.Find(&bindings).Error; err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	// Populate tags from CollectionTag table if empty using batch query
	_ = s.populateTagsForBindings(bindings)

	return bindings, nil
}

// UpdateBinding updates an existing binding
func (s *DuneService) UpdateBinding(id int64, updates map[string]interface{}) error {
	// Get the current binding to check what we're updating
	currentBinding, err := s.GetBinding(id)
	if err != nil {
		return fmt.Errorf("failed to get current binding: %w", err)
	}
	if currentBinding == nil {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	// Check for conflicts if Dune query ID is being updated
	if newDuneQueryID, exists := updates["dune_query_id"]; exists {
		if newDuneQueryID != currentBinding.DuneQueryID {
			var existingByQuery db.DuneTwitterBinding
			err := s.db.DB.Where("dune_query_id = ? AND id != ?", newDuneQueryID, id).First(&existingByQuery).Error
			if err == nil {
				return fmt.Errorf("dune query ID '%s' is already bound to another Twitter user", newDuneQueryID)
			}
			if err != gorm.ErrRecordNotFound {
				return fmt.Errorf("failed to check existing Dune query ID: %w", err)
			}
		}
	}

	// Perform the update (allow multiple bindings for same Twitter username)
	result := s.db.DB.Model(&db.DuneTwitterBinding{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update binding: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	log.Info().Int64("id", id).Msg("Updated Dune Twitter binding")
	return nil
}

// getTagsFromCollectionTag retrieves tags from CollectionTag table by Twitter username
func (s *DuneService) getTagsFromCollectionTag(twitterUsername string) (db.StringArray, error) {
	var collectionTags []db.CollectionTag
	err := s.db.DB.Where("twitter_username = ?", twitterUsername).Find(&collectionTags).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get tags for twitter username %s: %w", twitterUsername, err)
	}

	// Extract unique tag names
	tagSet := make(map[string]bool)
	for _, tag := range collectionTags {
		tagSet[tag.TagName] = true
	}

	var tags []string
	for tagName := range tagSet {
		tags = append(tags, tagName)
	}

	return db.StringArray(tags), nil
}

// getBatchTagsFromCollectionTag retrieves tags for multiple Twitter usernames in a single query
// This method solves the n+1 query problem by fetching all tags in one database query
func (s *DuneService) getBatchTagsFromCollectionTag(twitterUsernames []string) (map[string]db.StringArray, error) {
	if len(twitterUsernames) == 0 {
		return make(map[string]db.StringArray), nil
	}

	var collectionTags []db.CollectionTag
	err := s.db.DB.Where("twitter_username IN ?", twitterUsernames).Find(&collectionTags).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get batch tags: %w", err)
	}

	// Group tags by Twitter username
	tagsByUsername := make(map[string]map[string]bool)
	for _, tag := range collectionTags {
		if tagsByUsername[tag.TwitterUsername] == nil {
			tagsByUsername[tag.TwitterUsername] = make(map[string]bool)
		}
		tagsByUsername[tag.TwitterUsername][tag.TagName] = true
	}

	// Convert to StringArray format
	result := make(map[string]db.StringArray)
	for username, tagSet := range tagsByUsername {
		var tags []string
		for tagName := range tagSet {
			tags = append(tags, tagName)
		}
		result[username] = db.StringArray(tags)
	}

	return result, nil
}

// populateTagsForBindings populates tags for bindings that have empty tags
func (s *DuneService) populateTagsForBindings(bindings []*db.DuneTwitterBinding) error {
	// Collect usernames that need tag population
	var usernamesNeedingTags []string
	for _, binding := range bindings {
		if len(binding.Tags) == 0 {
			usernamesNeedingTags = append(usernamesNeedingTags, binding.TwitterUserName)
		}
	}

	if len(usernamesNeedingTags) == 0 {
		return nil
	}

	// Batch query tags
	tagsMap, err := s.getBatchTagsFromCollectionTag(usernamesNeedingTags)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get batch tags from collection_tags")
		return nil // Don't fail the entire operation
	}

	// Populate tags for bindings
	for _, binding := range bindings {
		if len(binding.Tags) == 0 {
			if tags, exists := tagsMap[binding.TwitterUserName]; exists {
				binding.Tags = tags
			}
		}
	}

	return nil
}

// DeleteBinding deletes a binding by ID
func (s *DuneService) DeleteBinding(id int64) error {
	result := s.db.DB.Delete(&db.DuneTwitterBinding{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete binding: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("binding with ID %d not found", id)
	}

	log.Info().Int64("id", id).Msg("Deleted Dune Twitter binding")
	return nil
}

// GetQueryResults retrieves query results for a specific query and date range
func (s *DuneService) GetQueryResults(duneQueryID string, startDate, endDate time.Time) ([]*db.DuneQueryResult, error) {
	var results []*db.DuneQueryResult
	err := s.db.DB.Where("dune_query_id = ? AND query_date >= ? AND query_date <= ?",
		duneQueryID, startDate, endDate).
		Order("query_date ASC").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %w", err)
	}
	return results, nil
}

// GetBatchQueryResults retrieves query results for multiple Dune query IDs within date range
// This method solves the n+1 query problem by fetching all results in a single database query
func (s *DuneService) GetBatchQueryResults(duneQueryIDs []string, startDate, endDate time.Time) (map[string][]*db.DuneQueryResult, error) {
	if len(duneQueryIDs) == 0 {
		return make(map[string][]*db.DuneQueryResult), nil
	}

	var results []*db.DuneQueryResult
	err := s.db.DB.Where("dune_query_id IN ? AND query_date >= ? AND query_date < ?",
		duneQueryIDs, startDate, endDate).
		Order("dune_query_id ASC, query_date ASC").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get batch query results: %w", err)
	}

	// Group results by query ID
	groupedResults := make(map[string][]*db.DuneQueryResult)
	for _, result := range results {
		groupedResults[result.DuneQueryID] = append(groupedResults[result.DuneQueryID], result)
	}

	return groupedResults, nil
}

func (s *DuneService) GetProjectId(twitterUserName, categoryIdStr string) (string, error) {
	var binding db.DuneTwitterBinding
	err := s.db.DB.Joins("JOIN dune_binding_categories bc ON dune_twitter_bindings.id = bc.binding_id").
		Where("dune_twitter_bindings.twitter_user_name = ? AND bc.category_id = ?", twitterUserName, categoryIdStr).
		Select("dune_twitter_bindings.id").
		First(&binding).Error

	if err != nil {
		return "", fmt.Errorf("failed to query binding: %w", err)
	}

	return cast.ToString(binding.ID), nil
}

// GetProjectData retrieves project data by Twitter username and date range
func (s *DuneService) GetProjectData(idStr string, startDate, endDate time.Time) (*ProjectDataResponse, error) {
	// Get bindings for the Twitter user
	if idStr == "" {
		return nil, fmt.Errorf("id is required")
	}
	binding, err := s.GetBindingByID(idStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get binding by ID: %w", err)
	}

	response := &ProjectDataResponse{
		ProjectName:       binding.ProjectName,
		ProjectLogo:       binding.ProjectLogo,
		TwitterUserName:   binding.TwitterUserName,
		Slug:              s.getSlugByUserName(binding.TwitterUserName), // Get slug from external API
		DuneQueryID:       binding.DuneQueryID,
		ContractAddresses: binding.ContractAddresses,
		QueryResults:      []*DuneQueryResultResponse{},
	}

	// Collect all query results from all bindings

	results, err := s.GetQueryResults(binding.DuneQueryID, startDate, endDate)
	if err != nil {
		log.Error().Err(err).Str("dune_query_id", binding.DuneQueryID).Msg("Failed to get query results")
		return nil, fmt.Errorf("failed to get query results: %w", err)
	}

	// Filter out today's data (only return data from yesterday and earlier)
	// Use UTC+8 timezone for today calculation
	// location, _ := time.LoadLocation("Asia/Shanghai")
	today := time.Now().UTC().Truncate(24 * time.Hour)
	filteredResults := []*db.DuneQueryResult{}
	for _, result := range results {
		resultDate := result.QueryDate.UTC().Truncate(24 * time.Hour)
		// if true {
		if resultDate.Before(today) {
			filteredResults = append(filteredResults, result)
		}
	}

	// Sort all results by query date in descending order (newest first)
	for i := 0; i < len(filteredResults)-1; i++ {
		for j := i + 1; j < len(filteredResults); j++ {
			if filteredResults[i].QueryDate.Before(filteredResults[j].QueryDate) {
				filteredResults[i], filteredResults[j] = filteredResults[j], filteredResults[i]
			}
		}
	}

	// Convert to response format (excluding ID, CreatedAt, UpdatedAt)
	responseResults := make([]*DuneQueryResultResponse, len(filteredResults))
	for i, result := range filteredResults {
		responseResults[i] = &DuneQueryResultResponse{
			QueryDate:           result.QueryDate,
			ContractInteraction: result.ContractInteraction,
			Users:               result.Users,
			Data:                result.Data,
		}
	}

	response.QueryResults = responseResults
	return response, nil
}

// ProjectDataResponse represents the response for project data queries
type ProjectDataResponse struct {
	Id                int64                      `json:"id"`
	ProjectName       string                     `json:"project_name"`
	ProjectLogo       string                     `json:"project_logo"`
	TwitterUserName   string                     `json:"twitter_user_name"`
	Slug              string                     `json:"slug"`
	DuneQueryID       string                     `json:"dune_query_id"`
	ChainIDs          db.StringArray             `json:"chain_ids"`
	ContractAddresses db.StringArray             `json:"contract_addresses"`
	Tags              []string                   `json:"tags"`
	QueryResults      []*DuneQueryResultResponse `json:"query_results,omitempty"` // For backward compatibility
	QueryResult       *DuneQueryResultResponse   `json:"query_result,omitempty"`  // New single result field
}

// DuneQueryResultResponse represents the filtered response for Dune query results
type DuneQueryResultResponse struct {
	QueryDate                     time.Time           `json:"query_date"`
	ContractInteraction           int                 `json:"contract_interaction"`
	Users                         int                 `json:"users"`
	Data                          db.JSONB            `json:"data,omitempty"`                             // Dynamic fields data
	ContractInteractionChangeRate *float64            `json:"contract_interaction_change_rate,omitempty"` // Contract interaction change rate compared to previous period
	UsersChangeRate               *float64            `json:"users_change_rate,omitempty"`                // Users change rate compared to previous period
	DataChangeRates               map[string]*float64 `json:"data_change_rates,omitempty"`                // Dynamic fields change rates
	ComparisonData                db.JSONB            `json:"comparison_data,omitempty"`                  // Historical period data for comparison
}

// FieldPeriodComparisonResponse represents period comparison for a specific binding field
type FieldPeriodComparisonResponse struct {
	BindingID       int64    `json:"binding_id"`
	DuneQueryID     string   `json:"dune_query_id"`
	ProjectName     string   `json:"project_name"`
	ProjectLogo     string   `json:"project_logo"`
	TwitterUserName string   `json:"twitter_user_name"`
	Field           string   `json:"field"`
	Days            int      `json:"days"`
	CurrentSum      float64  `json:"current_sum"`
	PreviousSum     float64  `json:"previous_sum"`
	ChangeRate      *float64 `json:"change_rate,omitempty"`
}

// CategoryFieldPeriodComparisonResponse represents period comparison for a specific category field
type CategoryFieldPeriodComparisonResponse struct {
	CategoryID          int64    `json:"category_id"`
	CategoryName        string   `json:"category_name"`
	CategoryDescription string   `json:"category_description"`
	Field               string   `json:"field"`
	Days                int      `json:"days"`
	BindingsCount       int      `json:"bindings_count"`
	CurrentSum          float64  `json:"current_sum"`
	PreviousSum         float64  `json:"previous_sum"`
	ChangeRate          *float64 `json:"change_rate,omitempty"`
}

// AllProjectsFieldSumComparisonResponse represents period comparison for all projects field sum
type AllProjectsFieldSumComparisonResponse struct {
	Field              string                   `json:"field,omitempty"` // Single field name when field is specified
	Days               int                      `json:"days"`
	TotalBindingsCount int                      `json:"total_bindings_count"`
	CurrentSum         float64                  `json:"current_sum,omitempty"`  // Single field sum when field is specified
	PreviousSum        float64                  `json:"previous_sum,omitempty"` // Single field sum when field is specified
	ChangeRate         *float64                 `json:"change_rate,omitempty"`  // Single field change rate when field is specified
	FieldsData         map[string]*FieldSumData `json:"fields_data,omitempty"`  // All fields data when field is not specified
	AnalysisPeriod     string                   `json:"analysis_period"`
	ComparisonPeriod   string                   `json:"comparison_period"`
}

// FieldSumData represents sum data for a specific field
type FieldSumData struct {
	CurrentSum  float64  `json:"current_sum"`
	PreviousSum float64  `json:"previous_sum"`
	ChangeRate  *float64 `json:"change_rate,omitempty"`
}

// CategoryFieldSumComparisonResponse represents period comparison for a specific category field sum
type CategoryFieldSumComparisonResponse struct {
	CategoryID          int64                    `json:"category_id"`
	CategoryName        string                   `json:"category_name"`
	CategoryDescription string                   `json:"category_description"`
	Field               string                   `json:"field,omitempty"` // Single field name when field is specified
	Days                int                      `json:"days"`
	BindingsCount       int                      `json:"bindings_count"`
	CurrentSum          float64                  `json:"current_sum,omitempty"`  // Single field sum when field is specified
	PreviousSum         float64                  `json:"previous_sum,omitempty"` // Single field sum when field is specified
	ChangeRate          *float64                 `json:"change_rate,omitempty"`  // Single field change rate when field is specified
	FieldsData          map[string]*FieldSumData `json:"fields_data,omitempty"`  // All fields data when field is not specified
	AnalysisPeriod      string                   `json:"analysis_period"`
	ComparisonPeriod    string                   `json:"comparison_period"`
}

// DailyFieldSumData represents sum data for a specific field on a specific day
type DailyFieldSumData struct {
	Date string  `json:"date"` // Date in YYYY-MM-DD format
	Sum  float64 `json:"sum"`  // Sum value for the day
}

// DailyFieldsData represents all fields data for a specific day
type DailyFieldsData struct {
	Date       string             `json:"date"`        // Date in YYYY-MM-DD format
	FieldsData map[string]float64 `json:"fields_data"` // All fields sum data for this date
}

// BaseDailyFieldSumResponse contains common fields for daily field sum responses
type BaseDailyFieldSumResponse struct {
	Field               string               `json:"field,omitempty"`             // Single field name when field is specified
	Days                int                  `json:"days"`                        // Number of days requested (0 for all)
	CategoryID          int64                `json:"category_id"`                 // Category ID (0 for all projects)
	CategoryName        string               `json:"category_name"`               // Category name (empty for all projects)
	CategoryDescription string               `json:"category_description"`        // Category description (empty for all projects)
	BindingsCount       int                  `json:"bindings_count"`              // Number of bindings (total for all projects, category count for specific category)
	DailyData           []*DailyFieldSumData `json:"daily_data,omitempty"`        // Daily aggregated data (when field is specified)
	DailyFieldsData     []*DailyFieldsData   `json:"daily_fields_data,omitempty"` // All fields daily data when field is not specified (time in outer layer)
	Period              string               `json:"period"`                      // Analysis period
}

// AllProjectsDailyFieldSumResponse represents daily field sum data for all projects
type AllProjectsDailyFieldSumResponse struct {
	BaseDailyFieldSumResponse
}

// CategoryDailyFieldSumResponse represents daily field sum data for a specific category
type CategoryDailyFieldSumResponse struct {
	BaseDailyFieldSumResponse
}

// FetchAllDuneData is the scheduled task that fetches data for all bindings
func (s *DuneService) FetchAllDuneData() {
	log.Info().Msg("Starting scheduled Dune data fetch")

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Info().Dur("duration", duration).Msg("Completed scheduled Dune data fetch")
	}()

	// Get all unique Dune query IDs
	var queryIDs []string
	err := s.db.DB.Model(&db.DuneTwitterBinding{}).
		Distinct("dune_query_id").
		Pluck("dune_query_id", &queryIDs).Error

	if err != nil {
		log.Error().Err(err).Msg("Failed to get Dune query IDs")
		return
	}

	log.Info().Int("query_count", len(queryIDs)).Msg("Fetching data for Dune queries")

	// Data structure to collect messages - use the global MessageData type
	var allMessages []MessageData
	var hasNewData bool

	// Fetch data for each query ID
	for _, queryID := range queryIDs {
		messageData, hasNew, err := s.fetchDuneQueryDataWithMessages(queryID)
		if err != nil {
			log.Error().Err(err).Str("query_id", queryID).Msg("Failed to fetch Dune query data")
			continue
		}

		if hasNew {
			hasNewData = true
			allMessages = append(allMessages, messageData...)
		}

		// Small delay between requests to avoid rate limiting
		time.Sleep(100 * time.Millisecond)
	}

	// Send consolidated messages after all queries are processed
	if hasNewData && s.notifyTelegram != nil {
		s.sendConsolidatedMessages(allMessages)
	}
}

// FetchDuneQueryData fetches and stores Dune query data for a specific query ID
func (s *DuneService) FetchDuneQueryData(queryID string) error {
	_, _, err := s.fetchDuneQueryDataWithMessages(queryID)
	return err
}

// MessageData represents the structure for collecting message data
type MessageData struct {
	ProjectName        string
	Date               string
	TotalNetBalance    interface{}
	Users              interface{}
	HasTotalNetBalance bool
	HasUsers           bool
}

// fetchDuneQueryDataWithMessages fetches data and returns message data instead of sending immediately
func (s *DuneService) fetchDuneQueryDataWithMessages(queryID string) ([]MessageData, bool, error) {
	// Get the combined field set for all bindings using this query
	allFields, err := s.getCombinedFieldsForQueryID(queryID)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get combined fields for query ID: %w", err)
	}

	log.Info().Str("query_id", queryID).Int("field_count", len(allFields)).Msg("Fetching Dune data with dynamic field parsing")

	// Fetch data from Dune API
	duneResponse, err := s.callDuneAPIStructured(queryID)
	if err != nil {
		return nil, false, fmt.Errorf("failed to call Dune API: %w", err)
	}

	var messageData []MessageData
	var savedCount, updatedCount int
	var hasNewRecords bool

	// Process each row in the response
	for _, row := range duneResponse.Result.Rows {
		// Handle date field mapping
		if dateVal, hasDate := row["Date"]; !hasDate || dateVal == "" || dateVal == nil {
			if dayVal, hasDay := row["day"]; hasDay && dayVal != nil && dayVal != "" {
				row["Date"] = dayVal
			}
		}

		// Parse the date from the row - handle multiple date formats
		var queryDate time.Time
		var parseErr error

		// Try different date formats
		dateFormats := []string{
			"2006-01-02 15:04:05.000 UTC", // Full timestamp with UTC
			"2006-01-02 15:04:05.000",     // Full timestamp without timezone
			"2006-01-02 15:04:05",         // Timestamp without milliseconds
			"2006-01-02",                  // Date only
		}

		if dateValue, ok := row["Date"]; ok && dateValue != nil {
			dateStr := cast.ToString(dateValue)
			for _, format := range dateFormats {
				if queryDate, parseErr = time.Parse(format, dateStr); parseErr == nil {
					break
				}
			}
		}

		if parseErr != nil {
			log.Warn().Str("query_id", queryID).Interface("date", row["Date"]).Msg("Failed to parse date, skipping row")
			continue
		}
		queryDate = queryDate.UTC().Truncate(24 * time.Hour)

		// Parse dynamic data based on field definitions
		dynamicData, legacyData, err := s.parseDuneRowData(row, allFields)
		if err != nil {
			log.Warn().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to parse row data, skipping")
			continue
		}

		// Check if record already exists for this queryID + date
		var existing db.DuneQueryResult
		err = s.db.DB.Where("dune_query_id = ? AND query_date = ?", queryID, queryDate).
			First(&existing).Error

		if err == nil {
			// Record exists, update it with new data
			existing.ContractInteraction = legacyData.ContractInteraction
			existing.Users = legacyData.Users
			existing.Data = dynamicData
			if err = s.db.DB.Save(&existing).Error; err != nil {
				log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to update existing record")
				continue
			}
			updatedCount++
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// Record doesn't exist, create new one
			result := &db.DuneQueryResult{
				DuneQueryID:         queryID,
				QueryDate:           queryDate,
				ContractInteraction: legacyData.ContractInteraction,
				Users:               legacyData.Users,
				Data:                dynamicData,
			}
			if err = s.db.DB.Create(result).Error; err != nil {
				log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to create new record")
				continue
			}
			savedCount++

			// Collect message data for new records
			if !hasNewRecords {
				var bindings []db.DuneTwitterBinding
				if err = s.db.DB.Where("dune_query_id = ?", queryID).Find(&bindings).Error; err == nil {
					for _, b := range bindings {
						msg := MessageData{
							ProjectName: b.ProjectName,
							Date:        queryDate.Format("2006-01-02"),
						}

						// Check for AUM data
						if val, ok := dynamicData["total_net_balance"]; ok {
							msg.TotalNetBalance = val
							msg.HasTotalNetBalance = true
						}

						// Check for Users data
						if val, ok := dynamicData["Users"]; ok {
							msg.Users = val
							msg.HasUsers = true
						}

						if msg.HasTotalNetBalance || msg.HasUsers {
							messageData = append(messageData, msg)
						}
					}
				}
			}
			hasNewRecords = true
		} else {
			log.Error().Err(err).Str("query_id", queryID).Time("date", queryDate).Msg("Failed to check existing record")
			continue
		}
	}

	log.Info().
		Str("query_id", queryID).
		Int("saved_count", savedCount).
		Int("updated_count", updatedCount).
		Int("total_rows", len(duneResponse.Result.Rows)).
		Msg("Successfully processed Dune query results")

	return messageData, hasNewRecords, nil
}

// sendConsolidatedMessages sends consolidated messages for AUM and Users data
func (s *DuneService) sendConsolidatedMessages(allMessages []MessageData) {
	if len(allMessages) == 0 {
		return
	}

	// Group messages by type
	var aumMessages []string
	var usersMessages []string

	for _, msg := range allMessages {
		if msg.HasTotalNetBalance {
			aumMsg := fmt.Sprintf("AUM 表数据： %s，日期：%s，Total Net AUM：%v",
				msg.ProjectName, msg.Date, msg.TotalNetBalance)
			aumMessages = append(aumMessages, aumMsg)
		}

		if msg.HasUsers {
			usersMsg := fmt.Sprintf("Users 表数据： %s，日期：%s，1D Users：%d",
				msg.ProjectName, msg.Date, cast.ToInt(msg.Users))
			usersMessages = append(usersMessages, usersMsg)
		}
	}

	// Send consolidated AUM message if any AUM data exists
	if len(aumMessages) > 0 {
		consolidatedAUM := fmt.Sprintf("🔄 AUM 数据更新汇总 (%d 条记录):\n%s",
			len(aumMessages), strings.Join(aumMessages, "\n"))
		_ = s.notifyTelegram.SendMessage(consolidatedAUM)
		time.Sleep(1 * time.Second) // Delay between message types
	}

	// Send consolidated Users message if any Users data exists
	if len(usersMessages) > 0 {
		consolidatedUsers := fmt.Sprintf("👥 Users 数据更新汇总 (%d 条记录):\n%s",
			len(usersMessages), strings.Join(usersMessages, "\n"))
		_ = s.notifyTelegram.SendMessage(consolidatedUsers)
	}

	log.Info().
		Int("aum_messages", len(aumMessages)).
		Int("users_messages", len(usersMessages)).
		Msg("Sent consolidated messages")
}

// callDuneAPIStructured calls the Dune API and returns structured response
func (s *DuneService) callDuneAPIStructured(queryID string) (*DuneQueryResults, error) {
	// Construct API URL
	apiURL := fmt.Sprintf("%s/api/v1/query/%s/results?limit=600", s.config.BaseURL, queryID)

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers if needed (API key, etc.)
	if s.config.APIKey == "" {
		log.Warn().Msg("Dune API key not set, requests may fail")
	}

	req.Header.Set("X-Dune-API-Key", s.config.APIKey)

	// Make request with retry logic
	var resp *http.Response
	for i := 0; i < s.config.MaxRetries; i++ {
		resp, err = client.Do(req)
		if err != nil {
			if i == s.config.MaxRetries-1 {
				return nil, fmt.Errorf("failed to make request after %d retries: %w", s.config.MaxRetries, err)
			}
			log.Warn().Err(err).Int("retry", i+1).Str("query_id", queryID).Msg("Request failed, retrying")
			time.Sleep(time.Duration(i+1) * time.Second)
			continue
		}
		break
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	// Parse response
	var result DuneQueryResults
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// aggregateResults calculates total contract interactions and users from query results
func (s *DuneService) aggregateResults(results []*db.DuneQueryResult) (totalInteractions, totalUsers int) {
	for _, result := range results {
		totalInteractions += result.ContractInteraction
		totalUsers += result.Users
	}
	return totalInteractions, totalUsers
}

// aggregateDynamicData aggregates dynamic field data from query results
func (s *DuneService) aggregateDynamicData(results []*db.DuneQueryResult, fields []db.DuneCategoryField) db.JSONB {
	if len(results) == 0 {
		return db.JSONB{}
	}

	// Initialize aggregated data map
	aggregatedData := make(map[string]interface{})

	// Create field configuration maps for type-aware aggregation
	fieldTypeMap := make(map[string]string)
	fieldAggregateSumMap := make(map[string]bool)
	for _, field := range fields {
		fieldTypeMap[field.Key] = field.Type
		fieldAggregateSumMap[field.Key] = *field.AggregateSum
	}

	// Aggregate each field across all results
	for _, result := range results {
		if result.Data == nil {
			continue
		}

		for key, value := range result.Data {
			if value == nil {
				continue
			}

			fieldType := fieldTypeMap[key]
			if fieldType == "" {
				fieldType = "float" // Default to float for unknown fields
			}

			// Check if this field should use sum aggregation (default to true for backward compatibility)
			useAggregateSum := fieldAggregateSumMap[key]

			// Convert value to appropriate type for aggregation
			floatVal, err := s.convertToFloat64(value)
			if err != nil {
				// For non-numeric fields, just use the last value
				aggregatedData[key] = value
				continue
			}

			if useAggregateSum {
				// Sum numeric values
				if existingVal, exists := aggregatedData[key]; exists {
					if existingFloat, err := s.convertToFloat64(existingVal); err == nil {
						aggregatedData[key] = existingFloat + floatVal
					}
				} else {
					aggregatedData[key] = floatVal
				}
			} else {
				// For non-aggregate fields, use the latest value (overwrite)
				aggregatedData[key] = floatVal
			}
		}
	}

	return db.JSONB(aggregatedData)
}

// PeriodComparisonConfig holds configuration for period comparison calculations
type PeriodComparisonConfig struct {
	CurrentStartDate    time.Time
	CurrentEndDate      time.Time
	ComparisonStartDate time.Time
	ComparisonEndDate   time.Time
}

// calculatePeriodDates calculates the date ranges for period comparison in UTC+8 timezone
func (s *DuneService) calculatePeriodDates(days int) PeriodComparisonConfig {
	utcPlus8 := time.FixedZone("UTC+8", 8*60*60)
	now := time.Now().In(utcPlus8)
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	if now.Hour() < 5 {
		today = today.AddDate(0, 0, -1)
	}

	// Current period: last N days (not including today)
	currentEndDate := today
	currentStartDate := today.AddDate(0, 0, -days)

	// Comparison period: previous N days before current period
	comparisonEndDate := currentStartDate
	comparisonStartDate := currentStartDate.AddDate(0, 0, -days)

	return PeriodComparisonConfig{
		CurrentStartDate:    currentStartDate,
		CurrentEndDate:      currentEndDate,
		ComparisonStartDate: comparisonStartDate,
		ComparisonEndDate:   comparisonEndDate,
	}
}

// sumFieldFromResults sums a specific field from query results, supporting both legacy and dynamic fields
func (s *DuneService) sumFieldFromResults(results []*db.DuneQueryResult, field string) float64 {
	if len(results) == 0 {
		return 0
	}

	// Helper to normalize keys
	normalize := func(s string) string {
		s = strings.TrimSpace(s)
		s = strings.ToLower(s)
		s = strings.ReplaceAll(s, " ", "_")
		s = strings.ReplaceAll(s, "-", "_")
		return s
	}

	target := normalize(field)

	// Legacy fields fast path
	if target == "contract_interaction" || target == "contractinteraction" {
		total := 0
		for _, r := range results {
			total += r.ContractInteraction
		}
		return float64(total)
	}
	if target == "users" {
		total := 0
		for _, r := range results {
			total += r.Users
		}
		return float64(total)
	}

	// Resolve dynamic data key by checking first records
	resolvedKey := ""
	for _, r := range results {
		if r.Data == nil {
			continue
		}
		if _, ok := r.Data[field]; ok {
			resolvedKey = field
			break
		}
		for k := range r.Data {
			if normalize(k) == target {
				resolvedKey = k
				break
			}
		}
		if resolvedKey != "" {
			break
		}
	}

	total := 0.0
	for _, r := range results {
		if r.Data == nil {
			continue
		}
		var val interface{}
		var ok bool
		if resolvedKey != "" {
			val, ok = r.Data[resolvedKey]
		} else {
			if v, exists := r.Data[field]; exists {
				val = v
				ok = true
			} else {
				for k, v := range r.Data {
					if normalize(k) == target {
						val = v
						ok = true
						break
					}
				}
			}
		}
		if !ok {
			continue
		}
		switch t := val.(type) {
		case float64:
			total += t
		case int:
			total += float64(t)
		case int64:
			total += float64(t)
		case json.Number:
			f, _ := t.Float64()
			total += f
		case string:
			if f, err := strconv.ParseFloat(t, 64); err == nil {
				total += f
			}
		default:
			// non-numeric ignored
		}
	}
	return total
}

// processBatchBindingsPeriodData processes period comparison data for multiple bindings with batch optimization
func (s *DuneService) processBatchBindingsPeriodData(
	bindings []*db.DuneTwitterBinding,
	currentResults, comparisonResults map[string][]*db.DuneQueryResult,
	periodConfig PeriodComparisonConfig,
	field string,
) ([]*ProjectDataResponse, error) {
	if len(bindings) == 0 {
		return []*ProjectDataResponse{}, nil
	}

	// Collect usernames that need tag population (only those with empty tags)
	var usernamesNeedingTags []string
	for _, binding := range bindings {
		if len(binding.Tags) == 0 {
			usernamesNeedingTags = append(usernamesNeedingTags, binding.TwitterUserName)
		}
	}

	// Batch fetch collection tags only for bindings that need them
	tagsMap := make(map[string][]string)
	if len(usernamesNeedingTags) > 0 {
		batchTags, err := s.getBatchTagsFromCollectionTag(usernamesNeedingTags)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to get batch tags from collection_tags")
		} else {
			// Convert StringArray to []string for compatibility
			for username, stringArray := range batchTags {
				tagsMap[username] = []string(stringArray)
			}
		}
	}

	// Process each binding with pre-fetched tags
	responses := make([]*ProjectDataResponse, 0, len(bindings))
	for _, binding := range bindings {
		// Handle tags: prioritize binding tags, fallback to pre-fetched collection tags
		tags := make([]string, 0)
		if len(binding.Tags) > 0 {
			// Use tags from binding (priority)
			tags = []string(binding.Tags)
		} else {
			// Use pre-fetched tags from collection_tags table
			if collectionTags, exists := tagsMap[binding.TwitterUserName]; exists {
				tags = collectionTags
			}
		}

		response, err := s.processBindingPeriodDataCore(binding, currentResults, comparisonResults, periodConfig, field, tags)
		if err != nil {
			log.Warn().Err(err).Str("dune_query_id", binding.DuneQueryID).
				Msg("Failed to process binding period data, skipping")
			continue
		}
		responses = append(responses, response)
	}

	return responses, nil
}

// processBindingPeriodData processes period comparison data for a single binding (legacy method for backward compatibility)
func (s *DuneService) processBindingPeriodData(
	binding *db.DuneTwitterBinding,
	currentResults, comparisonResults map[string][]*db.DuneQueryResult,
	periodConfig PeriodComparisonConfig,
	field string,
) (*ProjectDataResponse, error) {
	// Handle tags: prioritize binding tags, fallback to collection tags
	tags := make([]string, 0)
	if len(binding.Tags) > 0 {
		// Use tags from binding (priority)
		tags = []string(binding.Tags)
	} else {
		// Fallback to tags from CollectionTag table
		collectionTags, err := s.getTagsFromCollectionTag(binding.TwitterUserName)
		if err != nil {
			log.Warn().Err(err).Str("twitter_username", binding.TwitterUserName).
				Msg("Failed to get tags from collection_tags, using empty tags")
		} else {
			tags = []string(collectionTags)
		}
	}

	return s.processBindingPeriodDataCore(binding, currentResults, comparisonResults, periodConfig, field, tags)
}

// processBindingPeriodDataCore contains the core logic for processing binding period data
func (s *DuneService) processBindingPeriodDataCore(
	binding *db.DuneTwitterBinding,
	currentResults, comparisonResults map[string][]*db.DuneQueryResult,
	periodConfig PeriodComparisonConfig,
	field string,
	tags []string,
) (*ProjectDataResponse, error) {

	projectResponse := &ProjectDataResponse{
		Id:                binding.ID,
		ProjectName:       binding.ProjectName,
		ProjectLogo:       binding.ProjectLogo,
		TwitterUserName:   binding.TwitterUserName,
		Slug:              s.getSlugByUserName(binding.TwitterUserName), // Get slug from external API
		DuneQueryID:       binding.DuneQueryID,
		ChainIDs:          binding.ChainIDs,
		ContractAddresses: binding.ContractAddresses,
		Tags:              tags,
	}

	// Get current and comparison period results for this binding
	currentQueryResults := currentResults[binding.DuneQueryID]
	comparisonQueryResults := comparisonResults[binding.DuneQueryID]

	// Get all fields for this binding to support dynamic field aggregation
	allFields, err := s.getCombinedFieldsForQueryID(binding.DuneQueryID)
	if err != nil {
		log.Warn().Err(err).Str("dune_query_id", binding.DuneQueryID).
			Msg("Failed to get fields for dynamic aggregation, using empty field set")
		allFields = []db.DuneCategoryField{}
	}

	// Calculate aggregated metrics for both periods
	currentTotalInteractions, currentTotalUsers := s.aggregateResults(currentQueryResults)
	comparisonTotalInteractions, comparisonTotalUsers := s.aggregateResults(comparisonQueryResults)

	// Aggregate dynamic data for both periods
	currentAggregatedData := s.aggregateDynamicData(currentQueryResults, allFields)
	comparisonAggregatedData := s.aggregateDynamicData(comparisonQueryResults, allFields)

	// Generate comparison data that reflects the actual comparison values used
	// (aggregated for sum fields, latest for non-aggregate fields)
	comparisonDataForResponse := s.generateComparisonData(comparisonQueryResults, comparisonAggregatedData, allFields)

	// Create aggregated result response if we have current period data
	if len(currentQueryResults) > 0 {
		// Use the end date of current period as the query date
		resultResponse := &DuneQueryResultResponse{
			QueryDate:           periodConfig.CurrentEndDate.AddDate(0, 0, -1), // Last day of the period
			ContractInteraction: currentTotalInteractions,
			Users:               currentTotalUsers,
			Data:                currentAggregatedData,
		}

		// Calculate change rates for both contract interactions and users
		var contractChangeRate float64 = 0
		var usersChangeRate float64 = 0

		if comparisonTotalInteractions > 0 {
			contractChangeRate = float64(currentTotalInteractions-comparisonTotalInteractions) / float64(comparisonTotalInteractions) * 100
		}
		if comparisonTotalUsers > 0 {
			usersChangeRate = float64(currentTotalUsers-comparisonTotalUsers) / float64(comparisonTotalUsers) * 100
		}

		resultResponse.ContractInteractionChangeRate = &contractChangeRate
		resultResponse.UsersChangeRate = &usersChangeRate

		// Calculate dynamic field change rates and set comparison data
		if len(comparisonQueryResults) > 0 {
			dynamicChangeRates := s.calculateDynamicFieldChangeRatesWithLatestComparison(
				currentQueryResults, comparisonQueryResults,
				currentAggregatedData, comparisonAggregatedData,
				allFields,
			)
			resultResponse.DataChangeRates = dynamicChangeRates
			resultResponse.ComparisonData = comparisonDataForResponse
		}

		// Apply field filtering if field parameter is provided
		if field != "" {
			s.filterResponseByField(resultResponse, field)
		}

		projectResponse.QueryResult = resultResponse
	}

	// Log metrics for this binding
	log.Debug().
		Str("dune_query_id", binding.DuneQueryID).
		Str("twitter_user_name", binding.TwitterUserName).
		Int("current_interactions", currentTotalInteractions).
		Int("current_users", currentTotalUsers).
		Int("comparison_interactions", comparisonTotalInteractions).
		Int("comparison_users", comparisonTotalUsers).
		Msg("Processed binding data with period comparison analysis")

	return projectResponse, nil
}

// GetAllProjectsDataWithPeriodComparison retrieves aggregated data for all binding projects
// within specified days range and compares with the previous period of the same length
func (s *DuneService) GetAllProjectsDataWithPeriodComparison(days int) ([]*ProjectDataResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int("days", days).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting all projects data with period comparison")

	// Get all bindings without pagination
	bindings, err := s.GetAllBindingsWithoutPagination()
	if err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	if len(bindings) == 0 {
		log.Info().Msg("No bindings found, returning empty results")
		return []*ProjectDataResponse{}, nil
	}

	// Collect all unique Dune query IDs for batch processing
	duneQueryIDs := make([]string, 0, len(bindings))
	for _, binding := range bindings {
		duneQueryIDs = append(duneQueryIDs, binding.DuneQueryID)
	}

	// Batch fetch current period results
	currentResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period batch query results: %w", err)
	}

	// Batch fetch comparison period results
	comparisonResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period batch query results: %w", err)
	}

	log.Info().
		Int("bindings_count", len(bindings)).
		Int("current_results_count", len(currentResults)).
		Int("comparison_results_count", len(comparisonResults)).
		Msg("Retrieved batch query results for period comparison")

	// Process all bindings using batch optimization
	responses, err := s.processBatchBindingsPeriodData(bindings, currentResults, comparisonResults, periodConfig, "")
	if err != nil {
		return nil, fmt.Errorf("failed to process bindings period data: %w", err)
	}

	log.Info().
		Int("total_responses", len(responses)).
		Msg("Successfully completed GetAllProjectsDataWithPeriodComparison")

	return responses, nil
}

// GetBindingFieldPeriodComparison calculates period comparison for a specific binding and field
func (s *DuneService) GetBindingFieldPeriodComparison(bindingID int64, field string, days int) (*FieldPeriodComparisonResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}
	if strings.TrimSpace(field) == "" {
		return nil, fmt.Errorf("field is required")
	}

	// Fetch binding to get the dune query id and project info
	binding, err := s.GetBinding(bindingID)
	if err != nil {
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return nil, fmt.Errorf("binding not found")
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int("days", days).
		Int64("binding_id", bindingID).
		Str("dune_query_id", binding.DuneQueryID).
		Str("field", field).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting binding field data with period comparison")

	// Fetch results for current and comparison periods
	currentMap, err := s.GetBatchQueryResults([]string{binding.DuneQueryID}, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}
	comparisonMap, err := s.GetBatchQueryResults([]string{binding.DuneQueryID}, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	currentResults := currentMap[binding.DuneQueryID]
	comparisonResults := comparisonMap[binding.DuneQueryID]

	// Use the common field summing function
	currentSum := s.sumFieldFromResults(currentResults, field)
	previousSum := s.sumFieldFromResults(comparisonResults, field)

	var changeRate *float64
	if previousSum > 0 {
		rate := (currentSum - previousSum) / previousSum * 100
		changeRate = &rate
	} else {
		zero := 0.0
		changeRate = &zero
	}

	log.Info().
		Int64("binding_id", bindingID).
		Str("dune_query_id", binding.DuneQueryID).
		Str("field", field).
		Int("days", days).
		Float64("current_sum", currentSum).
		Float64("previous_sum", previousSum).
		Msg("Computed field period comparison")

	return &FieldPeriodComparisonResponse{
		BindingID:       bindingID,
		DuneQueryID:     binding.DuneQueryID,
		ProjectName:     binding.ProjectName,
		ProjectLogo:     binding.ProjectLogo,
		TwitterUserName: binding.TwitterUserName,
		Field:           field,
		Days:            days,
		CurrentSum:      currentSum,
		PreviousSum:     previousSum,
		ChangeRate:      changeRate,
	}, nil
}

// GetCategoryFieldPeriodComparison calculates period comparison for a specific category and field
func (s *DuneService) GetCategoryFieldPeriodComparison(categoryID int64, field string, days int) (*CategoryFieldPeriodComparisonResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}
	if strings.TrimSpace(field) == "" {
		return nil, fmt.Errorf("field is required")
	}

	// Fetch category to get the category info
	category, err := s.GetCategoryByID(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	if category == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Get all bindings associated with this category
	bindings, err := s.GetBindingsByCategory(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bindings for category: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found for category %d", categoryID)
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int("days", days).
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("bindings_count", len(bindings)).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting category field data with period comparison")

	// Collect all dune query IDs from bindings
	duneQueryIDs := make([]string, len(bindings))
	for i, binding := range bindings {
		duneQueryIDs[i] = binding.DuneQueryID
	}

	// Batch fetch results for current and comparison periods
	currentMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}
	comparisonMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	// Aggregate data from all bindings using the common field summing function
	var totalCurrentSum float64
	var totalPreviousSum float64

	for _, binding := range bindings {
		currentResults := currentMap[binding.DuneQueryID]
		comparisonResults := comparisonMap[binding.DuneQueryID]

		currentSum := s.sumFieldFromResults(currentResults, field)
		previousSum := s.sumFieldFromResults(comparisonResults, field)

		totalCurrentSum += currentSum
		totalPreviousSum += previousSum
	}

	// Calculate change rate
	var changeRate *float64
	if totalPreviousSum > 0 {
		rate := (totalCurrentSum - totalPreviousSum) / totalPreviousSum * 100
		changeRate = &rate
	} else {
		zero := 0.0
		changeRate = &zero
	}

	log.Info().
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Float64("current_sum", totalCurrentSum).
		Float64("previous_sum", totalPreviousSum).
		Msg("Computed category field period comparison")

	return &CategoryFieldPeriodComparisonResponse{
		CategoryID:          categoryID,
		CategoryName:        category.Name,
		CategoryDescription: category.Description,
		Field:               field,
		Days:                days,
		BindingsCount:       len(bindings),
		CurrentSum:          totalCurrentSum,
		PreviousSum:         totalPreviousSum,
		ChangeRate:          changeRate,
	}, nil
}

// GetCategoryProjectsDataWithPeriodComparison retrieves aggregated data for binding projects in a specific category
// within specified days range and compares with the previous period of the same length
// Returns the same data structure as GetAllProjectsDataWithPeriodComparison but filtered to category bindings
// If field is provided, only that field will be included in data and data_change_rates
func (s *DuneService) GetCategoryProjectsDataWithPeriodComparison(categoryID int64, days int, field string) ([]*ProjectDataResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}

	// Fetch category to validate it exists
	category, err := s.GetCategoryByID(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	if category == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Get all bindings associated with this category
	bindings, err := s.GetBindingsByCategory(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bindings for category: %w", err)
	}

	if len(bindings) == 0 {
		log.Info().Int64("category_id", categoryID).Msg("No bindings found for category, returning empty results")
		return []*ProjectDataResponse{}, nil
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting category projects data with period comparison")

	// Collect all unique Dune query IDs for batch processing
	duneQueryIDs := make([]string, 0, len(bindings))
	for _, binding := range bindings {
		duneQueryIDs = append(duneQueryIDs, binding.DuneQueryID)
	}

	// Batch fetch data for current and comparison periods
	currentResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}

	comparisonResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	// Process all bindings using batch optimization
	projectsData, err := s.processBatchBindingsPeriodData(bindings, currentResults, comparisonResults, periodConfig, field)
	if err != nil {
		return nil, fmt.Errorf("failed to process bindings period data: %w", err)
	}

	log.Info().
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Int("days", days).
		Int("projects_count", len(projectsData)).
		Msg("Successfully completed GetCategoryProjectsDataWithPeriodComparison")

	return projectsData, nil
}

// GetBindingProjectDataWithPeriodComparison retrieves aggregated data for a specific binding
// within specified days range and compares with the previous period of the same length
// Returns the same data structure as GetAllProjectsDataWithPeriodComparison but for a single binding
// If field is provided, only that field will be included in data and data_change_rates
func (s *DuneService) GetBindingProjectDataWithPeriodComparison(bindingID int64, days int, field string) (*ProjectDataResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}

	// Fetch binding to get the binding info
	binding, err := s.GetBinding(bindingID)
	if err != nil {
		return nil, fmt.Errorf("failed to get binding: %w", err)
	}
	if binding == nil {
		return nil, fmt.Errorf("binding not found")
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int64("binding_id", bindingID).
		Str("project_name", binding.ProjectName).
		Str("twitter_user_name", binding.TwitterUserName).
		Int("days", days).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting binding project data with period comparison")

	// Batch fetch data for current and comparison periods
	duneQueryIDs := []string{binding.DuneQueryID}

	currentResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}

	comparisonResults, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	// Process binding data using the common method
	projectResponse, err := s.processBindingPeriodData(binding, currentResults, comparisonResults, periodConfig, field)
	if err != nil {
		return nil, fmt.Errorf("failed to process binding period data: %w", err)
	}

	log.Info().
		Int64("binding_id", bindingID).
		Str("project_name", binding.ProjectName).
		Str("twitter_user_name", binding.TwitterUserName).
		Int("days", days).
		Msg("Successfully completed GetBindingProjectDataWithPeriodComparison")

	return projectResponse, nil
}

// GetAllProjectsFieldSumComparison calculates period comparison for all projects field sum
func (s *DuneService) GetAllProjectsFieldSumComparison(field string, days int) (*AllProjectsFieldSumComparisonResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}

	// Get all bindings
	bindings, err := s.GetAllBindingsWithoutPagination()
	if err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found")
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int("days", days).
		Str("field", field).
		Int("bindings_count", len(bindings)).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting all projects field sum data with period comparison")

	// Collect all dune query IDs from bindings
	duneQueryIDs := make([]string, len(bindings))
	for i, binding := range bindings {
		duneQueryIDs[i] = binding.DuneQueryID
	}

	// Batch fetch results for current and comparison periods
	currentMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}
	comparisonMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	// Format period strings for response
	analysisPeriod := fmt.Sprintf("%s to %s",
		periodConfig.CurrentStartDate.Format("2006-01-02"),
		periodConfig.CurrentEndDate.Format("2006-01-02"))
	comparisonPeriod := fmt.Sprintf("%s to %s",
		periodConfig.ComparisonStartDate.Format("2006-01-02"),
		periodConfig.ComparisonEndDate.Format("2006-01-02"))

	// If field is specified, return single field data
	if strings.TrimSpace(field) != "" {
		return s.getSingleFieldSumComparison(field, days, bindings, currentMap, comparisonMap, analysisPeriod, comparisonPeriod)
	}

	// If field is not specified, return all fields data
	return s.getAllFieldsSumComparison(days, bindings, currentMap, comparisonMap, analysisPeriod, comparisonPeriod)
}

// getSingleFieldSumComparison handles single field aggregation
func (s *DuneService) getSingleFieldSumComparison(field string, days int, bindings []*db.DuneTwitterBinding, currentMap, comparisonMap map[string][]*db.DuneQueryResult, analysisPeriod, comparisonPeriod string) (*AllProjectsFieldSumComparisonResponse, error) {
	// Aggregate data from all bindings using the common field summing function
	var totalCurrentSum float64
	var totalPreviousSum float64

	for _, binding := range bindings {
		currentResults := currentMap[binding.DuneQueryID]
		comparisonResults := comparisonMap[binding.DuneQueryID]

		currentSum := s.sumFieldFromResults(currentResults, field)
		previousSum := s.sumFieldFromResults(comparisonResults, field)

		totalCurrentSum += currentSum
		totalPreviousSum += previousSum
	}

	// Calculate change rate
	var changeRate *float64
	if totalPreviousSum > 0 {
		rate := (totalCurrentSum - totalPreviousSum) / totalPreviousSum * 100
		changeRate = &rate
	} else {
		zero := 0.0
		changeRate = &zero
	}

	log.Info().
		Str("field", field).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Float64("current_sum", totalCurrentSum).
		Float64("previous_sum", totalPreviousSum).
		Msg("Computed single field sum period comparison")

	return &AllProjectsFieldSumComparisonResponse{
		Field:              field,
		Days:               days,
		TotalBindingsCount: len(bindings),
		CurrentSum:         totalCurrentSum,
		PreviousSum:        totalPreviousSum,
		ChangeRate:         changeRate,
		AnalysisPeriod:     analysisPeriod,
		ComparisonPeriod:   comparisonPeriod,
	}, nil
}

// getAllFieldsSumComparison handles all fields aggregation
func (s *DuneService) getAllFieldsSumComparison(days int, bindings []*db.DuneTwitterBinding, currentMap, comparisonMap map[string][]*db.DuneQueryResult, analysisPeriod, comparisonPeriod string) (*AllProjectsFieldSumComparisonResponse, error) {
	// Define standard fields to aggregate
	standardFields := []string{"contract_interaction", "users"}

	// Collect all dynamic fields from all bindings
	dynamicFieldsSet := make(map[string]bool)
	for _, binding := range bindings {
		for _, result := range currentMap[binding.DuneQueryID] {
			if result.Data != nil {
				// result.Data is already a map[string]interface{} (db.JSONB)
				for fieldName := range result.Data {
					// Skip "day" and "Date" fields
					if fieldName != "day" && fieldName != "Date" {
						dynamicFieldsSet[fieldName] = true
					}
				}
			}
		}
	}

	// Convert dynamic fields set to slice
	dynamicFields := make([]string, 0, len(dynamicFieldsSet))
	for fieldName := range dynamicFieldsSet {
		dynamicFields = append(dynamicFields, fieldName)
	}

	// Combine all fields
	allFields := append(standardFields, dynamicFields...)

	// Aggregate data for all fields
	fieldsData := make(map[string]*FieldSumData)

	for _, fieldName := range allFields {
		var totalCurrentSum float64
		var totalPreviousSum float64

		for _, binding := range bindings {
			currentResults := currentMap[binding.DuneQueryID]
			comparisonResults := comparisonMap[binding.DuneQueryID]

			currentSum := s.sumFieldFromResults(currentResults, fieldName)
			previousSum := s.sumFieldFromResults(comparisonResults, fieldName)

			totalCurrentSum += currentSum
			totalPreviousSum += previousSum
		}

		// Calculate change rate for this field
		var changeRate *float64
		if totalPreviousSum > 0 {
			rate := (totalCurrentSum - totalPreviousSum) / totalPreviousSum * 100
			changeRate = &rate
		} else if totalCurrentSum > 0 {
			// If previous sum is 0 but current sum > 0, it's 100% increase
			rate := 100.0
			changeRate = &rate
		} else {
			// Both are 0, no change
			zero := 0.0
			changeRate = &zero
		}

		fieldsData[fieldName] = &FieldSumData{
			CurrentSum:  totalCurrentSum,
			PreviousSum: totalPreviousSum,
			ChangeRate:  changeRate,
		}
	}

	log.Info().
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("total_fields", len(allFields)).
		Strs("standard_fields", standardFields).
		Strs("dynamic_fields", dynamicFields).
		Msg("Computed all fields sum period comparison")

	return &AllProjectsFieldSumComparisonResponse{
		Days:               days,
		TotalBindingsCount: len(bindings),
		FieldsData:         fieldsData,
		AnalysisPeriod:     analysisPeriod,
		ComparisonPeriod:   comparisonPeriod,
	}, nil
}

// GetCategoryFieldSumComparison calculates period comparison for a specific category field sum
func (s *DuneService) GetCategoryFieldSumComparison(categoryID int64, field string, days int) (*CategoryFieldSumComparisonResponse, error) {
	if days <= 0 {
		return nil, fmt.Errorf("days must be greater than 0")
	}

	// Fetch category to get the category info
	category, err := s.GetCategoryByID(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	if category == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Get all bindings associated with this category
	bindings, err := s.GetBindingsByCategory(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bindings for category: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found for category %d", categoryID)
	}

	// Calculate date ranges
	periodConfig := s.calculatePeriodDates(days)

	log.Info().
		Int("days", days).
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("bindings_count", len(bindings)).
		Time("current_start", periodConfig.CurrentStartDate).
		Time("current_end", periodConfig.CurrentEndDate).
		Time("comparison_start", periodConfig.ComparisonStartDate).
		Time("comparison_end", periodConfig.ComparisonEndDate).
		Msg("Getting category field sum data with period comparison")

	// Collect all dune query IDs from bindings
	duneQueryIDs := make([]string, len(bindings))
	for i, binding := range bindings {
		duneQueryIDs[i] = binding.DuneQueryID
	}

	// Batch fetch results for current and comparison periods
	currentMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.CurrentStartDate, periodConfig.CurrentEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period results: %w", err)
	}
	comparisonMap, err := s.GetBatchQueryResults(duneQueryIDs, periodConfig.ComparisonStartDate, periodConfig.ComparisonEndDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get comparison period results: %w", err)
	}

	// Format period strings for response
	analysisPeriod := fmt.Sprintf("%s to %s",
		periodConfig.CurrentStartDate.Format("2006-01-02"),
		periodConfig.CurrentEndDate.Format("2006-01-02"))
	comparisonPeriod := fmt.Sprintf("%s to %s",
		periodConfig.ComparisonStartDate.Format("2006-01-02"),
		periodConfig.ComparisonEndDate.Format("2006-01-02"))

	// If field is specified, return single field data
	if strings.TrimSpace(field) != "" {
		return s.getCategorySingleFieldSumComparison(category, field, days, bindings, currentMap, comparisonMap, analysisPeriod, comparisonPeriod)
	}

	// If field is not specified, return all fields data
	return s.getCategoryAllFieldsSumComparison(category, days, bindings, currentMap, comparisonMap, analysisPeriod, comparisonPeriod)
}

// getCategorySingleFieldSumComparison handles single field aggregation for category
func (s *DuneService) getCategorySingleFieldSumComparison(category *db.DuneCategory, field string, days int, bindings []*db.DuneTwitterBinding, currentMap, comparisonMap map[string][]*db.DuneQueryResult, analysisPeriod, comparisonPeriod string) (*CategoryFieldSumComparisonResponse, error) {
	// Aggregate data from all bindings using the common field summing function
	var totalCurrentSum float64
	var totalPreviousSum float64

	for _, binding := range bindings {
		currentResults := currentMap[binding.DuneQueryID]
		comparisonResults := comparisonMap[binding.DuneQueryID]

		currentSum := s.sumFieldFromResults(currentResults, field)
		previousSum := s.sumFieldFromResults(comparisonResults, field)

		totalCurrentSum += currentSum
		totalPreviousSum += previousSum
	}

	// Calculate change rate
	var changeRate *float64
	if totalPreviousSum > 0 {
		rate := (totalCurrentSum - totalPreviousSum) / totalPreviousSum * 100
		changeRate = &rate
	} else {
		zero := 0.0
		changeRate = &zero
	}

	log.Info().
		Int64("category_id", category.ID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Float64("current_sum", totalCurrentSum).
		Float64("previous_sum", totalPreviousSum).
		Msg("Computed category single field sum period comparison")

	return &CategoryFieldSumComparisonResponse{
		CategoryID:          category.ID,
		CategoryName:        category.Name,
		CategoryDescription: category.Description,
		Field:               field,
		Days:                days,
		BindingsCount:       len(bindings),
		CurrentSum:          totalCurrentSum,
		PreviousSum:         totalPreviousSum,
		ChangeRate:          changeRate,
		AnalysisPeriod:      analysisPeriod,
		ComparisonPeriod:    comparisonPeriod,
	}, nil
}

// getCategoryAllFieldsSumComparison handles all fields aggregation for category
func (s *DuneService) getCategoryAllFieldsSumComparison(category *db.DuneCategory, days int, bindings []*db.DuneTwitterBinding, currentMap, comparisonMap map[string][]*db.DuneQueryResult, analysisPeriod, comparisonPeriod string) (*CategoryFieldSumComparisonResponse, error) {
	// Define standard fields to aggregate
	standardFields := []string{"contract_interaction", "users"}

	// Collect all dynamic fields from all bindings
	dynamicFieldsSet := make(map[string]bool)
	for _, binding := range bindings {
		for _, result := range currentMap[binding.DuneQueryID] {
			if result.Data != nil {
				// result.Data is already a map[string]interface{} (db.JSONB)
				for fieldName := range result.Data {
					// Skip "day" and "Date" fields
					if fieldName != "day" && fieldName != "Date" {
						dynamicFieldsSet[fieldName] = true
					}
				}
			}
		}
	}

	// Convert dynamic fields set to slice
	dynamicFields := make([]string, 0, len(dynamicFieldsSet))
	for fieldName := range dynamicFieldsSet {
		dynamicFields = append(dynamicFields, fieldName)
	}

	// Combine all fields
	allFields := append(standardFields, dynamicFields...)

	// Aggregate data for all fields
	fieldsData := make(map[string]*FieldSumData)

	for _, fieldName := range allFields {
		var totalCurrentSum float64
		var totalPreviousSum float64

		for _, binding := range bindings {
			currentResults := currentMap[binding.DuneQueryID]
			comparisonResults := comparisonMap[binding.DuneQueryID]

			currentSum := s.sumFieldFromResults(currentResults, fieldName)
			previousSum := s.sumFieldFromResults(comparisonResults, fieldName)

			totalCurrentSum += currentSum
			totalPreviousSum += previousSum
		}

		// Calculate change rate for this field
		var changeRate *float64
		if totalPreviousSum > 0 {
			rate := (totalCurrentSum - totalPreviousSum) / totalPreviousSum * 100
			changeRate = &rate
		} else if totalCurrentSum > 0 {
			// If previous sum is 0 but current sum > 0, it's 100% increase
			rate := 100.0
			changeRate = &rate
		} else {
			// Both are 0, no change
			zero := 0.0
			changeRate = &zero
		}

		fieldsData[fieldName] = &FieldSumData{
			CurrentSum:  totalCurrentSum,
			PreviousSum: totalPreviousSum,
			ChangeRate:  changeRate,
		}
	}

	log.Info().
		Int64("category_id", category.ID).
		Str("category_name", category.Name).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("total_fields", len(allFields)).
		Strs("standard_fields", standardFields).
		Strs("dynamic_fields", dynamicFields).
		Msg("Computed category all fields sum period comparison")

	return &CategoryFieldSumComparisonResponse{
		CategoryID:          category.ID,
		CategoryName:        category.Name,
		CategoryDescription: category.Description,
		Days:                days,
		BindingsCount:       len(bindings),
		FieldsData:          fieldsData,
		AnalysisPeriod:      analysisPeriod,
		ComparisonPeriod:    comparisonPeriod,
	}, nil
}

// GetAllProjectsDailyFieldSum calculates daily field sum for all projects
func (s *DuneService) GetAllProjectsDailyFieldSum(field string, days int) (*AllProjectsDailyFieldSumResponse, error) {
	if days < 0 {
		return nil, fmt.Errorf("days must be non-negative (0 for all data)")
	}

	// Get all bindings
	bindings, err := s.GetAllBindingsWithoutPagination()
	if err != nil {
		return nil, fmt.Errorf("failed to get all bindings: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found")
	}

	// Calculate date range (exclude today's data, same as period-comparison API)
	var startDate, endDate time.Time
	utcPlus8 := time.FixedZone("UTC+8", 8*60*60)
	now := time.Now().In(utcPlus8)
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	if now.Hour() < 5 {
		today = today.AddDate(0, 0, -1)
	}

	// End date is today (excluded), so data goes up to yesterday
	endDate = today
	if days == 0 {
		// Get all available data - find the earliest date
		var earliestDate time.Time
		err = s.db.DB.Model(&db.DuneQueryResult{}).
			Select("MIN(query_date)").
			Scan(&earliestDate).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get earliest date: %w", err)
		}
		startDate = earliestDate.Truncate(24 * time.Hour)
	} else {
		// Start from N days before today (not including today)
		startDate = endDate.AddDate(0, 0, -days)
	}

	log.Info().
		Int("days", days).
		Str("field", field).
		Int("bindings_count", len(bindings)).
		Time("start_date", startDate).
		Time("end_date", endDate).
		Msg("Getting all projects daily field sum data")

	// Collect all dune query IDs from bindings
	duneQueryIDs := make([]string, len(bindings))
	for i, binding := range bindings {
		duneQueryIDs[i] = binding.DuneQueryID
	}

	// Batch fetch results for the entire period (exclude today)
	allResults, err := s.GetBatchQueryResults(duneQueryIDs, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %w", err)
	}

	// Format period string for response
	period := fmt.Sprintf("%s to %s",
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02"))

	// If field is specified, return single field data
	if strings.TrimSpace(field) != "" {
		return s.getSingleFieldDailySum(field, days, bindings, allResults, startDate, endDate, period)
	}

	// If field is not specified, return all fields data
	return s.getAllFieldsDailySum(days, bindings, allResults, startDate, endDate, period)
}

// getSingleFieldDailySum handles single field daily aggregation
func (s *DuneService) getSingleFieldDailySum(field string, days int, bindings []*db.DuneTwitterBinding, allResults map[string][]*db.DuneQueryResult, startDate, endDate time.Time, period string) (*AllProjectsDailyFieldSumResponse, error) {
	// Group results by date
	dailyData := make(map[string]float64)

	// Initialize all dates with 0 (exclude endDate)
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		dailyData[dateStr] = 0
	}

	// Aggregate data for each day
	for _, binding := range bindings {
		results := allResults[binding.DuneQueryID]
		for _, result := range results {
			dateStr := result.QueryDate.Format("2006-01-02")
			if _, exists := dailyData[dateStr]; exists {
				fieldValue := s.getFieldValueFromResult(result, field)
				dailyData[dateStr] += fieldValue
			}
		}
	}

	// Convert to sorted slice (exclude endDate)
	dailySlice := make([]*DailyFieldSumData, 0, len(dailyData))
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		dailySlice = append(dailySlice, &DailyFieldSumData{
			Date: dateStr,
			Sum:  dailyData[dateStr],
		})
	}

	log.Info().
		Str("field", field).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("daily_records", len(dailySlice)).
		Msg("Computed single field daily sum")

	return &AllProjectsDailyFieldSumResponse{
		BaseDailyFieldSumResponse: BaseDailyFieldSumResponse{
			Field:               field,
			Days:                days,
			CategoryID:          0,  // 0 for all projects
			CategoryName:        "", // empty for all projects
			CategoryDescription: "", // empty for all projects
			BindingsCount:       len(bindings),
			DailyData:           dailySlice,
			Period:              period,
		},
	}, nil
}

// getAllFieldsDailySum handles all fields daily aggregation
func (s *DuneService) getAllFieldsDailySum(days int, bindings []*db.DuneTwitterBinding, allResults map[string][]*db.DuneQueryResult, startDate, endDate time.Time, period string) (*AllProjectsDailyFieldSumResponse, error) {
	// Define standard fields to aggregate
	standardFields := []string{"contract_interaction", "users"}

	// Collect all dynamic fields from all bindings
	dynamicFieldsSet := make(map[string]bool)
	for _, binding := range bindings {
		for _, result := range allResults[binding.DuneQueryID] {
			if result.Data != nil {
				// result.Data is already a map[string]interface{} (db.JSONB)
				for fieldName := range result.Data {
					// Skip "day" and "Date" fields
					if fieldName != "day" && fieldName != "Date" {
						dynamicFieldsSet[fieldName] = true
					}
				}
			}
		}
	}

	// Convert dynamic fields set to slice
	dynamicFields := make([]string, 0, len(dynamicFieldsSet))
	for fieldName := range dynamicFieldsSet {
		dynamicFields = append(dynamicFields, fieldName)
	}

	// Combine all fields
	allFields := append(standardFields, dynamicFields...)

	// Aggregate data with time as outer layer
	dailyFieldsData := make([]*DailyFieldsData, 0)

	// Process each date (exclude endDate)
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		fieldsDataForDate := make(map[string]float64)

		// Initialize all fields with 0 for this date
		for _, fieldName := range allFields {
			fieldsDataForDate[fieldName] = 0
		}

		// Aggregate data for this date across all bindings
		for _, binding := range bindings {
			results := allResults[binding.DuneQueryID]
			for _, result := range results {
				resultDateStr := result.QueryDate.Format("2006-01-02")
				if resultDateStr == dateStr {
					// Aggregate all fields for this date
					for _, fieldName := range allFields {
						fieldValue := s.getFieldValueFromResult(result, fieldName)
						fieldsDataForDate[fieldName] += fieldValue
					}
				}
			}
		}

		// Create DailyFieldsData for this date
		dailyFieldsData = append(dailyFieldsData, &DailyFieldsData{
			Date:       dateStr,
			FieldsData: fieldsDataForDate,
		})
	}

	log.Info().
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("total_fields", len(allFields)).
		Strs("standard_fields", standardFields).
		Strs("dynamic_fields", dynamicFields).
		Msg("Computed all fields daily sum")

	return &AllProjectsDailyFieldSumResponse{
		BaseDailyFieldSumResponse: BaseDailyFieldSumResponse{
			Days:                days,
			CategoryID:          0,  // 0 for all projects
			CategoryName:        "", // empty for all projects
			CategoryDescription: "", // empty for all projects
			BindingsCount:       len(bindings),
			DailyFieldsData:     dailyFieldsData,
			Period:              period,
		},
	}, nil
}

// getFieldValueFromResult extracts field value from a query result
func (s *DuneService) getFieldValueFromResult(result *db.DuneQueryResult, field string) float64 {
	switch field {
	case "contract_interaction":
		return float64(result.ContractInteraction)
	case "users":
		return float64(result.Users)
	default:
		// Dynamic field from Data JSONB
		if result.Data != nil {
			if value, exists := result.Data[field]; exists {
				if floatVal, err := s.convertToFloat64(value); err == nil {
					return floatVal
				}
			}
		}
		return 0
	}
}

// GetCategoryDailyFieldSum calculates daily field sum for a specific category
func (s *DuneService) GetCategoryDailyFieldSum(categoryID int64, field string, days int) (*CategoryDailyFieldSumResponse, error) {
	if days < 0 {
		return nil, fmt.Errorf("days must be non-negative (0 for all data)")
	}

	// Fetch category to get the category info
	category, err := s.GetCategoryByID(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}
	if category == nil {
		return nil, fmt.Errorf("category not found")
	}

	// Get all bindings associated with this category
	bindings, err := s.GetBindingsByCategory(categoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bindings for category: %w", err)
	}

	if len(bindings) == 0 {
		return nil, fmt.Errorf("no bindings found for category %d", categoryID)
	}

	// Calculate date range (exclude today's data, same as period-comparison API)
	var startDate, endDate time.Time
	utcPlus8 := time.FixedZone("UTC+8", 8*60*60)
	now := time.Now().In(utcPlus8)
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	if now.Hour() < 5 {
		today = today.AddDate(0, 0, -1)
	}

	// End date is today (excluded), so data goes up to yesterday
	endDate = today
	if days == 0 {
		// Get all available data - find the earliest date
		var earliestDate time.Time
		err = s.db.DB.Model(&db.DuneQueryResult{}).
			Select("MIN(query_date)").
			Scan(&earliestDate).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get earliest date: %w", err)
		}
		startDate = earliestDate.Truncate(24 * time.Hour)
	} else {
		// Start from N days before today (not including today)
		startDate = endDate.AddDate(0, 0, -days)
	}

	log.Info().
		Int("days", days).
		Int64("category_id", categoryID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("bindings_count", len(bindings)).
		Time("start_date", startDate).
		Time("end_date", endDate).
		Msg("Getting category daily field sum data")

	// Collect all dune query IDs from bindings
	duneQueryIDs := make([]string, len(bindings))
	for i, binding := range bindings {
		duneQueryIDs[i] = binding.DuneQueryID
	}

	// Batch fetch results for the entire period (exclude today)
	allResults, err := s.GetBatchQueryResults(duneQueryIDs, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %w", err)
	}

	// Format period string for response
	period := fmt.Sprintf("%s to %s",
		startDate.Format("2006-01-02"),
		endDate.Format("2006-01-02"))

	// If field is specified, return single field data
	if strings.TrimSpace(field) != "" {
		return s.getCategorySingleFieldDailySum(category, field, days, bindings, allResults, startDate, endDate, period)
	}

	// If field is not specified, return all fields data
	return s.getCategoryAllFieldsDailySum(category, days, bindings, allResults, startDate, endDate, period)
}

// getCategorySingleFieldDailySum handles single field daily aggregation for category
func (s *DuneService) getCategorySingleFieldDailySum(category *db.DuneCategory, field string, days int, bindings []*db.DuneTwitterBinding, allResults map[string][]*db.DuneQueryResult, startDate, endDate time.Time, period string) (*CategoryDailyFieldSumResponse, error) {
	// Group results by date
	dailyData := make(map[string]float64)

	// Initialize all dates with 0
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		dailyData[dateStr] = 0
	}

	// Aggregate data for each day
	for _, binding := range bindings {
		results := allResults[binding.DuneQueryID]
		for _, result := range results {
			dateStr := result.QueryDate.Format("2006-01-02")
			if _, exists := dailyData[dateStr]; exists {
				fieldValue := s.getFieldValueFromResult(result, field)
				dailyData[dateStr] += fieldValue
			}
		}
	}

	// Convert to sorted slice
	dailySlice := make([]*DailyFieldSumData, 0, len(dailyData))
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		dailySlice = append(dailySlice, &DailyFieldSumData{
			Date: dateStr,
			Sum:  dailyData[dateStr],
		})
	}

	log.Info().
		Int64("category_id", category.ID).
		Str("category_name", category.Name).
		Str("field", field).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("daily_records", len(dailySlice)).
		Msg("Computed category single field daily sum")

	return &CategoryDailyFieldSumResponse{
		BaseDailyFieldSumResponse: BaseDailyFieldSumResponse{
			Field:               field,
			Days:                days,
			CategoryID:          category.ID,
			CategoryName:        category.Name,
			CategoryDescription: category.Description,
			BindingsCount:       len(bindings),
			DailyData:           dailySlice,
			Period:              period,
		},
	}, nil
}

// getCategoryAllFieldsDailySum handles all fields daily aggregation for category
func (s *DuneService) getCategoryAllFieldsDailySum(category *db.DuneCategory, days int, bindings []*db.DuneTwitterBinding, allResults map[string][]*db.DuneQueryResult, startDate, endDate time.Time, period string) (*CategoryDailyFieldSumResponse, error) {
	// Define standard fields to aggregate
	standardFields := []string{"contract_interaction", "users"}

	// Collect all dynamic fields from all bindings
	dynamicFieldsSet := make(map[string]bool)
	for _, binding := range bindings {
		for _, result := range allResults[binding.DuneQueryID] {
			if result.Data != nil {
				// result.Data is already a map[string]interface{} (db.JSONB)
				for fieldName := range result.Data {
					// Skip "day" and "Date" fields
					if fieldName != "day" && fieldName != "Date" {
						dynamicFieldsSet[fieldName] = true
					}
				}
			}
		}
	}

	// Convert dynamic fields set to slice
	dynamicFields := make([]string, 0, len(dynamicFieldsSet))
	for fieldName := range dynamicFieldsSet {
		dynamicFields = append(dynamicFields, fieldName)
	}

	// Combine all fields
	allFields := append(standardFields, dynamicFields...)

	// Aggregate data with time as outer layer
	dailyFieldsData := make([]*DailyFieldsData, 0)

	// Process each date (exclude endDate)
	for d := startDate; d.Before(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		fieldsDataForDate := make(map[string]float64)

		// Initialize all fields with 0 for this date
		for _, fieldName := range allFields {
			fieldsDataForDate[fieldName] = 0
		}

		// Aggregate data for this date across all bindings
		for _, binding := range bindings {
			results := allResults[binding.DuneQueryID]
			for _, result := range results {
				resultDateStr := result.QueryDate.Format("2006-01-02")
				if resultDateStr == dateStr {
					// Aggregate all fields for this date
					for _, fieldName := range allFields {
						fieldValue := s.getFieldValueFromResult(result, fieldName)
						fieldsDataForDate[fieldName] += fieldValue
					}
				}
			}
		}

		// Create DailyFieldsData for this date
		dailyFieldsData = append(dailyFieldsData, &DailyFieldsData{
			Date:       dateStr,
			FieldsData: fieldsDataForDate,
		})
	}

	log.Info().
		Int64("category_id", category.ID).
		Str("category_name", category.Name).
		Int("days", days).
		Int("bindings_count", len(bindings)).
		Int("total_fields", len(allFields)).
		Strs("standard_fields", standardFields).
		Strs("dynamic_fields", dynamicFields).
		Msg("Computed category all fields daily sum")

	return &CategoryDailyFieldSumResponse{
		BaseDailyFieldSumResponse: BaseDailyFieldSumResponse{
			Days:                days,
			CategoryID:          category.ID,
			CategoryName:        category.Name,
			CategoryDescription: category.Description,
			BindingsCount:       len(bindings),
			DailyFieldsData:     dailyFieldsData,
			Period:              period,
		},
	}, nil
}
