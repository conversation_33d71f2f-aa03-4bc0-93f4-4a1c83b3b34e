package api

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/metrics"
	"real-time-ca-service/internal/services"
)

// NewRouter creates a new HTTP router
func NewRouter(twitterService *services.TwitterService, caService *services.CAService, tokenService *services.TokenService, redisService *services.RedisService, duneService *services.DuneService, cfg *config.Config) http.Handler {
	// Create handler
	handler := NewHandler(twitterService, caService, tokenService, redisService, duneService)

	// Create router with default middleware (Logger, <PERSON>)
	if cfg.Server.GinMode == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}
	router := gin.Default()

	// Add custom middleware
	router.Use(corsMiddleware())
	router.Use(metricsMiddleware())
	router.Use(customLoggerMiddleware())

	router.GET("/api-docs/swagger.json", func(c *gin.Context) {
		if _, err := os.Stat("docs/swagger.json"); os.IsNotExist(err) {
			c.File("../../docs/swagger.json")
			return
		} else if err != nil {
			c.AbortWithStatusJSON(500, gin.H{"error": "Internal server error"})
			return
		}
		c.File("docs/swagger.json")
	})

	// Swagger documentation route
	// Uncomment when Swagger dependencies are added
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.URL("/api-docs/swagger.json")))

	// Set up metrics endpoint if enabled
	if cfg.Metrics.Enabled {
		router.GET(cfg.Metrics.Path, gin.WrapH(promhttp.Handler()))
		log.Info().Str("path", cfg.Metrics.Path).Msg("Metrics endpoint enabled")
	}

	// API routes group
	api := router.Group("/api")
	{
		// Register routes
		api.GET("/health", handler.HealthCheck)
		api.GET("/tweets", handler.GetTweets)
		api.GET("/tweet/:tweet_id", handler.GetTweetByID)
		api.GET("/recognized-cas", handler.GetRecognizedCAs)
		api.GET("/recognized-ca/:address/:chain_id", handler.GetRecognizedCAByAddressAndChainID)
		// social data webhook
		api.POST("/webhook/twitter", handler.HandleWebhook)
		api.GET("/list-tweets", handler.GetListTweets)
		api.GET("/announcement-statistics", handler.GetAnnouncementStatistics)
		api.GET("/announcement-statistics/total", handler.GetAnnouncementStatisticsTotal)
		// Announcement statistics hide list management
		api.GET("/admin/announcement-statistics/hide-list", handler.GetAnnouncementStatisticsHideList)
		api.POST("/admin/announcement-statistics/hide-list", handler.SetAnnouncementStatisticsHideList)
		api.GET("/telegram-notification-stats", handler.GetTelegramNotificationStats)

		// Excluded Twitter users management
		api.POST("/excluded-twitter-users", handler.AddExcludedTwitterUser)
		api.GET("/excluded-twitter-users", handler.GetExcludedTwitterUsers)
		api.DELETE("/excluded-twitter-users/:twitter_user_name", handler.RemoveExcludedTwitterUser)
		// Collections cache management
		api.POST("/trigger-collections-cache", handler.TriggerCollectionsCacheUpdate)
		api.GET("/cached-collections", handler.GetCachedCollections)
		api.GET("/collection-tags", handler.GetCollectionTags)

		// Dune service routes
		api.POST("/dune/bindings", handler.CreateDuneBinding)
		api.GET("/dune/bindings", handler.GetDuneBindings)
		api.GET("/dune/bindings/:id", handler.GetDuneBinding)
		api.PUT("/dune/bindings/:id", handler.UpdateDuneBinding)
		api.DELETE("/dune/bindings/:id", handler.DeleteDuneBinding)
		api.GET("/dune/project-data/:id", handler.GetDuneProjectData)
		api.GET("/dune/projects/period-comparison", handler.GetAllProjectsDataWithPeriodComparison)
		api.GET("/dune/bindings/:id/period-comparison", handler.GetBindingFieldPeriodComparison)
		api.POST("/dune/fetch-all", handler.TriggerDuneFetchAll)

		// Dune category management routes
		api.POST("/dune/categories", handler.CreateDuneCategory)
		api.GET("/dune/categories", handler.GetDuneCategories)
		api.GET("/dune/categories/:id", handler.GetDuneCategory)
		api.PUT("/dune/categories/:id", handler.UpdateDuneCategory)
		api.GET("/dune/categories/:id/project-data/:user_name", handler.GetDuneProjectDataByTwitterUser)
		api.DELETE("/dune/categories/:id", handler.DeleteDuneCategory)
		api.GET("/dune/categories/:id/bindings", handler.GetBindingsByCategory)
		api.GET("/dune/categories/:id/period-comparison", handler.GetCategoryFieldPeriodComparison)
		api.GET("/dune/all-projects/field-sum-comparison", handler.GetAllProjectsFieldSumComparison)
		api.GET("/dune/categories/:id/field-sum-comparison", handler.GetCategoryFieldSumComparison)
		api.GET("/dune/all-projects/daily-field-sum", handler.GetAllProjectsDailyFieldSum)
		api.GET("/dune/categories/:id/daily-field-sum", handler.GetCategoryDailyFieldSum)

		// Dune binding-category association routes
		api.PUT("/dune/bindings/:id/categories", handler.AssociateBindingWithCategories)
		api.GET("/dune/bindings/:id/categories", handler.GetBindingWithCategories)

		// api.DELETE("/recognized-cas/:address", handler.DeleteRecognizedCA)
		// api.POST("/recognized-cas", handler.AddRecognizedCA)
	}

	return router
}

// customLoggerMiddleware logs all requests
func customLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Skip logging for health checks to reduce noise
		if c.Request.URL.Path != "/api/health" {
			// Calculate latency
			duration := time.Since(start)

			// Log request details
			log.Info().
				Str("method", c.Request.Method).
				Str("path", c.Request.URL.Path).
				Int("status", c.Writer.Status()).
				Dur("duration", duration).
				Str("ip", c.ClientIP()).
				Str("user_agent", c.Request.UserAgent()).
				Msg("Request processed")
		}
	}
}

// metricsMiddleware collects metrics for all requests
func metricsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Skip metrics for health checks to reduce noise
		if c.Request.URL.Path != "/api/health" {
			// Calculate latency
			duration := time.Since(start)

			// Record metrics
			metrics.APIRequestsTotal.WithLabelValues(c.Request.URL.Path, c.Request.Method, http.StatusText(c.Writer.Status())).Inc()
			metrics.APIRequestDuration.WithLabelValues(c.Request.URL.Path, c.Request.Method).Observe(duration.Seconds())
		}
	}
}

// corsMiddleware adds CORS headers
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add CORS headers
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		// Process request
		c.Next()
	}
}
