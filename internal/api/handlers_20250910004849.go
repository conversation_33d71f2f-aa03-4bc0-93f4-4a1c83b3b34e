package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"real-time-ca-service/internal/db"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/services"
)

// Handler contains the API handlers
type Handler struct {
	twitterService *services.TwitterService
	caService      *services.CAService
	tokenService   *services.TokenService
	redisService   *services.RedisService
	duneService    *services.DuneService
	scheduler      *cron.Cron
}

// NewHandler creates a new Handler
func NewHandler(twitterService *services.TwitterService, caService *services.CAService, tokenService *services.TokenService, redisService *services.RedisService, duneService *services.DuneService) *Handler {
	return &Handler{
		twitterService: twitterService,
		caService:      caService,
		tokenService:   tokenService,
		redisService:   redisService,
		duneService:    duneService,
		scheduler:      cron.New(cron.WithLocation(time.FixedZone("UTC+8", 8*60*60))),
	}
}

// TweetResponse represents a tweet response
type TweetResponse struct {
	TweetInfo
	ContractAddresses []CAResponse `json:"contract_addresses,omitempty"`
}

type TweetInfo struct {
	ID                 string         `json:"id"`
	Text               string         `json:"text"`
	PublishedAt        int64          `json:"published_at"`
	User               UserResponse   `json:"user"`
	Images             []string       `json:"images"`
	Tags               []string       `json:"tags,omitempty"`
	CollectionTags     []string       `json:"collection_tags,omitempty"`
	ReplyCount         int            `json:"reply_count"`
	RetweetCount       int            `json:"retweet_count"`
	FavoriteCount      int            `json:"favorite_count"`
	ViewsCount         int            `json:"views_count"`
	BookmarkCount      int            `json:"bookmark_count"`
	ContentType        string         `json:"content_type"`     // "tweet" or "article"
	SourceListType     string         `json:"source_list_type"` // "KOLs" or "Projects"
	ArticleTitle       string         `json:"article_title,omitempty"`
	ArticlePreviewText string         `json:"article_preview_text,omitempty"`
	ArticleCoverURL    string         `json:"article_cover_url,omitempty"`
	BulletPoints       interface{}    `json:"bullet_points,omitempty"`
	Notices            NoticeResponse `json:"notices,omitempty"`
}

type NoticeResponse struct {
	IsProductUpdate        bool `json:"is_product_update"`
	IsBusinessData         bool `json:"is_business_data"`
	IsEcosystemPartnership bool `json:"is_ecosystem_partnership"`
	IsProfitOpportunity    bool `json:"is_profit_opportunity"`
	IsIndustryEvent        bool `json:"is_industry_event"`
	IsOthers               bool `json:"is_others"`
}

// UserResponse represents a user response
type UserResponse struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	ScreenName      string `json:"screen_name"`
	ProfileImageURL string `json:"profile_image_url"`
	IsVerified      bool   `json:"is_verified"`
	FollowersCount  int    `json:"followers_count"`
}

// CAResponse represents a contract address response
type CAResponse struct {
	Address           string                  `json:"address"`
	ChainType         string                  `json:"chain_type"`
	IsRecognized      bool                    `json:"is_recognized"`
	TokenDetails      []*TokenDetailsResponse `json:"token_details,omitempty"` // Changed to array
	TradeTokenDetails []TradeTokenInfo        `json:"trade_token_details,omitempty"`
	Tweets            []TweetInfo             `json:"tweets,omitempty"`
	Tags              []string                `json:"tags,omitempty"`
}

// TokenDetailsResponse represents token details response
type TokenDetailsResponse struct {
	Name          string   `json:"name"`
	Symbol        string   `json:"symbol"`
	ChainID       string   `json:"chain_id"`
	Source        string   `json:"source"` // Added source field
	LogoURL       string   `json:"logo_url"`
	TwitterURL    string   `json:"twitter_url"`
	PairCreatedAt int64    `json:"pair_created_at"`
	HolderCount   *int64   `json:"holder_count"`
	MarketCapUSD  *float64 `json:"market_cap_usd"`
	PriceUSD      *float64 `json:"price_usd"`
}

// RecognizedCARequest represents a request to add a recognized CA
type RecognizedCARequest struct {
	CAAddress     string `json:"ca_address"`
	ChainID       string `json:"chain_id"`
	TokenNameHint string `json:"token_name_hint"`
}

// GetTweets godoc
// @Summary      Get tweets
// @Description  Retrieves a list of tweets
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        limit   query     int     false  "Number of tweets to return (default: 20)"
// @Param        offset  query     int     false  "Offset for pagination (default: 0)"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Param        collection_tags    query     []string  false  "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200     {array}   TweetResponse
// @Failure      500     {object}  map[string]string
// @Router       /tweets [get]
func (h *Handler) GetTweets(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	tagsStr := c.Query("tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering tweets by tags")
	}

	if len(tags) == 0 {
		tags = []string{"ai agent ca"}
	}

	// Get tweets from the database with tag filtering
	tweets, err := h.twitterService.GetTweetsByTags(limit, offset, tags)
	if err != nil {
		log.Error().Err(err).Msg("Error getting tweets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting tweets"})
		return
	}

	// Convert to response format
	response := make([]TweetResponse, 0, len(tweets))
	for _, tweet := range tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		// Format contract addresses
		contractAddresses := make([]CAResponse, 0, len(tweet.ExtractedCAs))
		for _, ca := range tweet.ExtractedCAs {
			caResponse := CAResponse{
				Address:   ca.CAAddress,
				ChainType: ca.ChainType,
				// IsRecognized will be set based on token details
			}

			if len(ca.TokenDetails) > 0 {
				caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
				caResponse.IsRecognized = true

				// Process each token detail
				for _, tokenDetail := range ca.TokenDetails {
					tdResponse := &TokenDetailsResponse{
						Name:         tokenDetail.TokenName,
						Symbol:       tokenDetail.Symbol,
						ChainID:      tokenDetail.ChainID,
						Source:       tokenDetail.Source,
						LogoURL:      tokenDetail.TokenLogoURL,
						TwitterURL:   tokenDetail.TokenTwitterURL,
						HolderCount:  tokenDetail.HolderCount,
						MarketCapUSD: tokenDetail.MarketCapUSD,
						PriceUSD:     tokenDetail.PriceUSD,
					}

					// Format pair created at time
					if tokenDetail.PairCreatedAt != nil {
						tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
					}

					caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
				}
			}

			contractAddresses = append(contractAddresses, caResponse)
		}

		// Create tweet response
		tweetResponse := TweetResponse{
			TweetInfo: TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images: images,
			},
			ContractAddresses: contractAddresses,
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
		}

		response = append(response, tweetResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

func (h *Handler) AddRecognizedCA(c *gin.Context) {
	// Parse request body
	var req RecognizedCARequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate request
	if req.CAAddress == "" || req.ChainID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "CA address and chain ID are required"})
		return
	}

	// Add recognized CA
	err := h.caService.AddRecognizedCA(req.CAAddress, req.ChainID, req.TokenNameHint)
	if err != nil {
		log.Error().Err(err).Msg("Error adding recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error adding recognized CA"})
		return
	}

	// Return success
	c.JSON(http.StatusCreated, gin.H{"status": "success"})
}

// GetRecognizedCAs godoc
// @Summary      Get recognized contract addresses
// @Description  Retrieves a list of recognized contract addresses
// @Tags         contract-addresses
// @Accept       json
// @Produce      json
// @Param        limit   query     int     false  "Number of CAs to return (default: 20)"
// @Param        offset  query     int     false  "Offset for pagination (default: 0)"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200     {array}   CAResponse
// @Failure      500     {object}  map[string]string
// @Router       /recognized-cas [get]
func (h *Handler) GetRecognizedCAs(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	tagsStr := c.Query("tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering CAs by tags")
	}

	if len(tags) == 0 {
		tags = []string{"ai agent ca"}
	}

	// Get recognized CAs from the database with tag filtering
	cas, err := h.caService.GetRecognizedCAsByTags(limit, offset, tags)
	if err != nil {
		log.Error().Err(err).Msg("Error getting recognized CAs")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting recognized CAs"})
		return
	}

	// Convert to response format
	response := make([]CAResponse, 0, len(cas))
	for _, ca := range cas {
		caResponse := CAResponse{
			Address:   ca.CAAddress,
			ChainType: ca.ChainType,
		}

		// Add CA tags
		if len(ca.Tags) > 0 {
			caResponse.Tags = make([]string, 0, len(ca.Tags))
			for _, tag := range ca.Tags {
				caResponse.Tags = append(caResponse.Tags, tag.Name)
			}
		}

		for _, tweet := range ca.Tweets {
			// Extract images from tweet JSON
			images := extractImagesFromTweet(tweet.FullTweetJSON)

			// Create tweet info
			tweetInfo := TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images: images,
			}

			// Add tweet tags if available
			if len(tweet.Tags) > 0 {
				tweetInfo.Tags = make([]string, 0, len(tweet.Tags))
				for _, tag := range tweet.Tags {
					tweetInfo.Tags = append(tweetInfo.Tags, tag.Name)
				}
			}

			// @dev @notice 此处暂时没有查询需求，先注释，有必要再进行优化
			// Add collection tags for the Twitter user
			// if tweet.User != nil {
			// 	collectionTags, err := h.twitterService.GetTagsByTwitterUsername(tweet.User.ScreenName)
			// 	if err != nil {
			// 		log.Error().Err(err).Str("screen_name", tweet.User.ScreenName).Msg("Error getting collection tags for user")
			// 	} else if len(collectionTags) > 0 {
			// 		tweetInfo.CollectionTags = collectionTags
			// 	}
			// }

			caResponse.Tweets = append(caResponse.Tweets, tweetInfo)
		}

		// Add token details if available
		if len(ca.TokenDetails) > 0 {
			caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
			caResponse.IsRecognized = true

			// Process each token detail
			for _, tokenDetail := range ca.TokenDetails {
				tdResponse := &TokenDetailsResponse{
					Name:         tokenDetail.TokenName,
					Symbol:       tokenDetail.Symbol,
					ChainID:      tokenDetail.ChainID,
					Source:       tokenDetail.Source,
					LogoURL:      tokenDetail.TokenLogoURL,
					TwitterURL:   tokenDetail.TokenTwitterURL,
					HolderCount:  tokenDetail.HolderCount,
					MarketCapUSD: tokenDetail.MarketCapUSD,
					PriceUSD:     tokenDetail.PriceUSD,
				}

				// Format pair created at time
				if tokenDetail.PairCreatedAt != nil {
					tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
				}

				caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
			}
		}

		response = append(response, caResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

func (h *Handler) DeleteRecognizedCA(c *gin.Context) {
	// Get address from URL
	address := c.Param("address")
	if address == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Address is required"})
		return
	}

	// Delete recognized CA
	err := h.caService.DeleteRecognizedCA(address)
	if err != nil {
		log.Error().Err(err).Msg("Error deleting recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error deleting recognized CA"})
		return
	}

	// Return success
	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// WebhookPayload represents the structure of the webhook payload
type WebhookPayload struct {
	Event string         `json:"event"`
	Data  services.Tweet `json:"data"`
	Meta  struct {
		MonitorID      string `json:"monitor_id"`
		MonitorType    string `json:"monitor_type"`
		MonitoredQuery string `json:"monitored_query"`
	} `json:"meta"`
}

// HandleWebhook godoc
// @Summary      Handle Twitter webhook
// @Description  Processes webhook events from Twitter
// @Tags         webhooks
// @Accept       json
// @Produce      json
// @Success      200
// @Router       /webhook/twitter [post]
func (h *Handler) HandleWebhook(c *gin.Context) {
	// Parse the webhook payload
	var payload WebhookPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		log.Error().Err(err).Msg("Error parsing webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook payload"})
		return
	}

	// Verify this is a new tweet event
	if payload.Event != "new_tweet" {
		log.Info().Str("event", payload.Event).Msg("Received non-tweet webhook event")
		c.Status(http.StatusOK) // Still return 200 to acknowledge receipt
		return
	}

	log.Info().
		Str("tweet_id", payload.Data.IdStr).
		Str("user", payload.Data.User.ScreenName).
		Str("event", payload.Event).
		Msg("Received tweet webhook")

	switch {
	case strings.HasPrefix(payload.Meta.MonitoredQuery, "list:"):
		parts := strings.Split(strings.TrimPrefix(payload.Meta.MonitoredQuery, "list:"), " ")
		listID := parts[0]
		if listID == "" {
			log.Warn().Msgf("Received list query with empty ID: %s", payload.Meta.MonitoredQuery)
			break // Break from switch, do not process further
		}

		var listType string
		foundList := false
		for _, listConfig := range h.twitterService.Config.TwitterLists {
			if listConfig.ID == listID {
				listType = listConfig.Type
				foundList = true
				break
			}
		}

		if foundList && listType != "" {
			log.Info().Msgf("Processing tweet for list ID: %s, Type: %s", listID, listType)
			go h.twitterService.ProcessListTweet(payload.Data, listType)
		} else {
			log.Warn().Msgf("No matching Twitter list configuration found for ID: %s from MonitoredQuery: %s. Payload: %+v", listID, payload.Meta.MonitoredQuery, payload.Data)
		}
	default:
		// Process the tweet with default webhook tag
		log.Info().Msgf("Processing tweet with default webhook tag: %s", payload.Meta.MonitoredQuery)
		go h.twitterService.ProcessTweet(payload.Data, payload.Meta.MonitoredQuery)
	}

	// Return success
	c.Status(http.StatusOK)
}

// GetRecognizedCAByAddressAndChainID godoc
// @Summary      Get a single recognized contract address
// @Description  Retrieves a specific recognized contract address by its address and chain ID
// @Tags         contract-addresses
// @Accept       json
// @Produce      json
// @Param        address  path      string  true  "Contract address"
// @Param        chain_id path      string  true  "Chain ID"
// @Success      200     {object}  CAResponse
// @Failure      404     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /recognized-ca/{address}/{chain_id} [get]
func (h *Handler) GetRecognizedCAByAddressAndChainID(c *gin.Context) {
	// Get address and chain_id from URL
	address := c.Param("address")
	chainID := c.Param("chain_id")

	if address == "" || chainID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Address and chain ID are required"})
		return
	}

	// Get recognized CA from the database
	ca, err := h.caService.GetRecognizedCAByAddressAndChainID(address, chainID)
	if err != nil {
		log.Error().Err(err).Msg("Error getting recognized CA")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting recognized CA"})
		return
	}

	// If no CA found, return 404
	if ca == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Recognized CA not found"})
		return
	}

	// Convert to response format
	caResponse := CAResponse{
		Address:   ca.CAAddress,
		ChainType: ca.ChainType,
	}

	for _, tag := range ca.Tags {
		caResponse.Tags = append(caResponse.Tags, tag.Name)
	}

	for _, tweet := range ca.Tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		tweetInfo := TweetInfo{
			ID:          tweet.TweetID,
			Text:        tweet.TextContent,
			PublishedAt: tweet.PublishedAt.Unix(),
			User: UserResponse{
				ID:              tweet.User.UserID,
				Name:            tweet.User.Name,
				ScreenName:      tweet.User.ScreenName,
				ProfileImageURL: tweet.User.ProfileImageURL,
				IsVerified:      tweet.User.IsVerified,
				FollowersCount:  tweet.User.FollowersCount,
			},
			Images: images,
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetInfo.Tags = append(tweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetInfo.CollectionTags = tweet.CollectionTags
		}

		caResponse.Tweets = append(caResponse.Tweets, tweetInfo)
	}

	// Add token details if available
	if len(ca.TokenDetails) > 0 {
		caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
		caResponse.IsRecognized = true

		// Process each token detail
		for _, tokenDetail := range ca.TokenDetails {
			tdResponse := &TokenDetailsResponse{
				Name:         tokenDetail.TokenName,
				Symbol:       tokenDetail.Symbol,
				ChainID:      tokenDetail.ChainID,
				Source:       tokenDetail.Source,
				LogoURL:      tokenDetail.TokenLogoURL,
				TwitterURL:   tokenDetail.TokenTwitterURL,
				HolderCount:  tokenDetail.HolderCount,
				MarketCapUSD: tokenDetail.MarketCapUSD,
				PriceUSD:     tokenDetail.PriceUSD,
			}

			// Format pair created at time
			if tokenDetail.PairCreatedAt != nil {
				tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
			}

			caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
		}
	}

	// Return JSON response
	c.JSON(http.StatusOK, caResponse)
}

// GetTweetByID godoc
// @Summary      Get a tweet by ID
// @Description  Retrieves a single tweet by its ID
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        tweet_id path      string  true  "Tweet ID"
// @Success      200     {object}  TweetResponse
// @Failure      404     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /tweet/{tweet_id} [get]
func (h *Handler) GetTweetByID(c *gin.Context) {
	// Get tweet_id from URL
	tweetID := c.Param("tweet_id")

	if tweetID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tweet ID is required"})
		return
	}

	// Get tweet from the database
	tweet, err := h.twitterService.GetTweetByID(tweetID)
	if err != nil {
		log.Error().Err(err).Str("tweet_id", tweetID).Msg("Error getting tweet")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting tweet"})
		return
	}

	// If no tweet found, return 404
	if tweet == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Tweet not found"})
		return
	}

	// Extract images from tweet JSON
	images := extractImagesFromTweet(tweet.FullTweetJSON)

	// Convert to response format
	tweetResponse := TweetResponse{
		TweetInfo: TweetInfo{
			ID:          tweet.TweetID,
			Text:        tweet.TextContent,
			PublishedAt: tweet.PublishedAt.Unix(),
			User: UserResponse{
				ID:              tweet.User.UserID,
				Name:            tweet.User.Name,
				ScreenName:      tweet.User.ScreenName,
				ProfileImageURL: tweet.User.ProfileImageURL,
				IsVerified:      tweet.User.IsVerified,
				FollowersCount:  tweet.User.FollowersCount,
			},
			Images:             images,
			ReplyCount:         tweet.ReplyCount,
			RetweetCount:       tweet.RetweetCount,
			FavoriteCount:      tweet.FavoriteCount,
			ViewsCount:         tweet.ViewsCount,
			BookmarkCount:      tweet.BookmarkCount,
			ContentType:        tweet.ContentType,
			ArticleTitle:       tweet.ArticleTitle,
			ArticlePreviewText: tweet.ArticlePreviewText,
			ArticleCoverURL:    tweet.ArticleCoverURL,
		},
		ContractAddresses: []CAResponse{},
	}

	// Add tags if available
	if len(tweet.Tags) > 0 {
		tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
		for _, tag := range tweet.Tags {
			tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
		}
	}

	// Add collection tags if available
	if len(tweet.CollectionTags) > 0 {
		tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
	}

	// Add contract addresses if available
	if len(tweet.ExtractedCAs) > 0 {
		for _, ca := range tweet.ExtractedCAs {
			caResponse := CAResponse{
				Address:   ca.CAAddress,
				ChainType: ca.ChainType,
			}

			// Add token details if available
			if len(ca.TokenDetails) > 0 {
				caResponse.TokenDetails = make([]*TokenDetailsResponse, 0, len(ca.TokenDetails))
				caResponse.IsRecognized = true

				// Process each token detail
				for _, tokenDetail := range ca.TokenDetails {
					tdResponse := &TokenDetailsResponse{
						Name:         tokenDetail.TokenName,
						Symbol:       tokenDetail.Symbol,
						ChainID:      tokenDetail.ChainID,
						Source:       tokenDetail.Source,
						LogoURL:      tokenDetail.TokenLogoURL,
						TwitterURL:   tokenDetail.TokenTwitterURL,
						HolderCount:  tokenDetail.HolderCount,
						MarketCapUSD: tokenDetail.MarketCapUSD,
						PriceUSD:     tokenDetail.PriceUSD,
					}

					// Format pair created at time
					if tokenDetail.PairCreatedAt != nil {
						tdResponse.PairCreatedAt = tokenDetail.PairCreatedAt.Unix()
					}

					caResponse.TokenDetails = append(caResponse.TokenDetails, tdResponse)
				}
			}

			tweetResponse.ContractAddresses = append(tweetResponse.ContractAddresses, caResponse)
		}
	}

	// Return JSON response
	c.JSON(http.StatusOK, tweetResponse)
}

// HealthCheck godoc
// @Summary      Health check endpoint
// @Description  Returns 200 OK if the service is healthy
// @Tags         system
// @Accept       json
// @Produce      json
// @Success      200  {object}  map[string]string
// @Router       /health [get]
func (h *Handler) HealthCheck(c *gin.Context) {
	// Simple health check that returns 200 OK
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// GetListTweets godoc
// @Summary      Get AI Agent related tweets
// @Description  Retrieves a list of tweets related to AI Agent with filtering options
// @Tags         tweets
// @Accept       json
// @Produce      json
// @Param        limit         query     int     false  "Number of tweets to return (default: 20)"
// @Param        offset        query     int     false  "Offset for pagination (default: 0)"
// @Param        content_type  query     string  false  "Filter by content type (tweet, article, ALL)"
// @Param        source_type   query     string  false  "Filter by source type (KOLs, Projects, ALL)"
// @Param        notice_type   query     string  false  "Filter by notice_type"
// @Param        user_id       query     string  false  "Filter by user_id"
// @Param        user_name       query     string  false  "Filter by user_name"
// @Param        tags    query     []string  false  "Filter by tags (comma-separated, e.g. 'tag1,tag2')"
// @Param        collection_tags    query     []string  false  "Filter by collection tags (comma-separated, e.g. 'tag1,tag2')"
// @Success      200          {array}   TweetResponse
// @Failure      500          {object}  map[string]string
// @Router       /list-tweets [get]
func (h *Handler) GetListTweets(c *gin.Context) {
	// Parse query parameters
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")
	contentType := c.Query("content_type")
	sourceType := c.Query("source_type")
	noticeType := c.Query("notice_type")
	userId := c.Query("user_id")
	userName := c.Query("user_name")
	tagsStr := c.Query("tags")
	collectionTagsStr := c.Query("collection_tags")

	limit := 20 // Default limit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		parsedOffset, err := strconv.Atoi(offsetStr)
		if err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	// Parse tags parameter (comma-separated)
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range tags {
			tags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("tags", tags).Msg("Filtering CAs by tags")
	}

	// Parse collection_tags parameter (comma-separated)
	var collectionTags []string
	if collectionTagsStr != "" {
		collectionTags = strings.Split(collectionTagsStr, ",")
		// Trim whitespace from each tag
		for i, tag := range collectionTags {
			collectionTags[i] = strings.TrimSpace(tag)
		}
		log.Debug().Strs("collection_tags", collectionTags).Msg("Filtering tweets by collection tags")
	}

	// Get AI Agent tweets from the database with filtering
	req := db.GetListTweetsRequest{
		Limit:          limit,
		Offset:         offset,
		ContentType:    contentType,
		SourceType:     sourceType,
		NoticeType:     noticeType,
		UserID:         userId,
		UserName:       userName,
		Tags:           tags,
		CollectionTags: collectionTags,
	}
	tweets, err := h.twitterService.GetListTweets(req)
	if err != nil {
		log.Error().Err(err).Msg("Error getting AI Agent tweets")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting AI Agent tweets"})
		return
	}

	// Initialize CA responses slice
	var caResponses []CAResponse

	// Handle user_name parameter to get CA information
	if userName != "" && len(tweets) > 0 {
		// 1. Get CA by username
		caByUserResp, err := h.getCaByUserName(userName)
		if err != nil {
			log.Error().Err(err).Str("username", userName).Msg("Error getting CA by username")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting CA by username"})
			return
		}

		// 2. Get token details for each CA address and merge into single response
		var tradeTokenDetails []TradeTokenInfo
		var mainAddress string
		var mainChainType string
		seenChains := make(map[int]bool) // Track processed chain IDs to avoid duplicates

		for _, caData := range caByUserResp.Data.Data {
			tokenResp, err := h.getTokenInfo(caData.Address)
			if err != nil {
				log.Error().Err(err).Str("address", caData.Address).Msg("Error getting token info")
				continue
			}

			// Convert to TokenDetailsResponse format
			if len(tokenResp.Data.List) > 0 {
				tokenData := tokenResp.Data.List[0]

				// Skip if we've already processed this chain ID
				if seenChains[tokenData.ChainId] {
					continue
				}
				seenChains[tokenData.ChainId] = true

				// Set main address and chain type from first valid token
				if mainAddress == "" {
					mainAddress = tokenData.Address
					switch tokenData.ChainId {
					case 10000:
						mainChainType = "solana"
					default:
						mainChainType = "evm"
					}
				}

				tradeTokenDetails = append(tradeTokenDetails, tokenResp.Data.List...)
			}
		}

		// Create single CAResponse with all token details
		if len(tradeTokenDetails) > 0 {
			caResponse := CAResponse{
				Address:           mainAddress,
				ChainType:         mainChainType,
				IsRecognized:      true,
				TradeTokenDetails: tradeTokenDetails,
			}
			caResponses = append(caResponses, caResponse)
		}

	}

	// Convert to response format
	response := make([]TweetResponse, 0, len(tweets))
	for _, tweet := range tweets {
		// Extract images from tweet JSON
		images := extractImagesFromTweet(tweet.FullTweetJSON)

		// Create tweet response
		tweetResponse := TweetResponse{
			TweetInfo: TweetInfo{
				ID:          tweet.TweetID,
				Text:        tweet.TextContent,
				PublishedAt: tweet.PublishedAt.Unix(),
				User: UserResponse{
					ID:              tweet.User.UserID,
					Name:            tweet.User.Name,
					ScreenName:      tweet.User.ScreenName,
					ProfileImageURL: tweet.User.ProfileImageURL,
					IsVerified:      tweet.User.IsVerified,
					FollowersCount:  tweet.User.FollowersCount,
				},
				Images:             images,
				ReplyCount:         tweet.ReplyCount,
				RetweetCount:       tweet.RetweetCount,
				FavoriteCount:      tweet.FavoriteCount,
				ViewsCount:         tweet.ViewsCount,
				BookmarkCount:      tweet.BookmarkCount,
				ContentType:        tweet.ContentType,
				SourceListType:     tweet.SourceListType,
				ArticleTitle:       tweet.ArticleTitle,
				ArticlePreviewText: tweet.ArticlePreviewText,
				ArticleCoverURL:    tweet.ArticleCoverURL,
				BulletPoints:       tweet.BulletPoints,
			},
		}

		// Add tweet tags if available
		if len(tweet.Tags) > 0 {
			tweetResponse.TweetInfo.Tags = make([]string, 0, len(tweet.Tags))
			for _, tag := range tweet.Tags {
				tweetResponse.TweetInfo.Tags = append(tweetResponse.TweetInfo.Tags, tag.Name)
			}
		}

		// Add collection tags if available
		if len(tweet.CollectionTags) > 0 {
			tweetResponse.TweetInfo.CollectionTags = tweet.CollectionTags
		}

		if tweet.SourceListType != "KOLs" {
			tweetResponse.Notices = NoticeResponse{
				IsProductUpdate:        tweet.IsProductUpdate,
				IsBusinessData:         tweet.IsBusinessData,
				IsEcosystemPartnership: tweet.IsEcosystemPartnership,
				IsProfitOpportunity:    tweet.IsProfitOpportunity,
				IsIndustryEvent:        tweet.IsIndustryEvent,
				IsOthers:               tweet.IsOthers,
			}
		}

		// Add CA responses to tweet if available for username queries
		if userName != "" && len(caResponses) > 0 {
			tweetResponse.ContractAddresses = caResponses
		}

		response = append(response, tweetResponse)
	}

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

// UserAnnouncementStatsResponse represents the announcement statistics for a specific Twitter user
type UserAnnouncementStatsResponse struct {
	TwitterUser               TwitterUserInfo `json:"twitter_user"`
	ProductUpdatesCount       int64           `json:"product_updates_count"`
	BusinessDataCount         int64           `json:"business_data_count"`
	EcosystemPartnershipCount int64           `json:"ecosystem_partnership_count"`
	ProfitOpportunityCount    int64           `json:"profit_opportunity_count"`
	IndustryEventsCount       int64           `json:"industry_events_count"`
	OthersCount               int64           `json:"others_count"`
	TotalAnnouncementsCount   int64           `json:"total_announcements_count"`
	DaysSinceLastTweet        *int            `json:"days_since_last_tweet"`
}

// TwitterUserInfo represents basic Twitter user information
type TwitterUserInfo struct {
	UserID          string   `json:"user_id"`
	ScreenName      string   `json:"screen_name"`
	Name            string   `json:"name"`
	ProfileImageURL string   `json:"profile_image_url"`
	Tags            []string `json:"tags"`
}

// getAnnouncementStatsHideSetKey builds the Redis key for the hide list (non-expiring)
func (h *Handler) getAnnouncementStatsHideSetKey() string {
	// Use Redis key prefix to avoid collisions
	return fmt.Sprintf("%sannouncement_stats_hide_set", h.redisService.GetConfig().KeyPrefix)
}

// HideListRequest represents the request body for setting the hide list
type HideListRequest struct {
	Usernames []string `json:"usernames"`
}

// SetAnnouncementStatisticsHideList handles POST /api/admin/announcement-statistics/hide-list
// @Summary Set announcement statistics hide list
// @Description Overwrite the hide user list for announcement statistics with the provided usernames array. The existing list is cleared and replaced.
// @Tags statistics
// @Accept json
// @Produce json
// @Param request body HideListRequest true "Usernames to set as hidden"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/announcement-statistics/hide-list [post]
func (h *Handler) SetAnnouncementStatisticsHideList(c *gin.Context) {
	ctx := context.Background()

	var req HideListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid JSON body: {\"usernames\": [\"name1\", \"name2\"]}"})
		return
	}

	// Build slice to insert directly from request body
	newMembers := make([]interface{}, 0, len(req.Usernames))
	for _, u := range req.Usernames {
		newMembers = append(newMembers, u)
	}

	key := h.getAnnouncementStatsHideSetKey()

	// Overwrite: DEL then SADD (no TTL)
	if err := h.redisService.GetClient().Del(ctx, key).Err(); err != nil {
		log.Error().Err(err).Str("key", key).Msg("Failed to clear hide set")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to clear existing list"})
		return
	}
	if len(newMembers) > 0 {
		if err := h.redisService.GetClient().SAdd(ctx, key, newMembers...).Err(); err != nil {
			log.Error().Err(err).Str("key", key).Msg("Failed to set new hide set")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to set new list"})
			return
		}
	}

	members, err := h.redisService.GetClient().SMembers(ctx, key).Result()
	if err != nil {
		log.Error().Err(err).Str("key", key).Msg("Failed to read hide set after update")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to fetch list"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "ok", "list": members})
}

// GetAnnouncementStatisticsHideList handles GET /api/admin/announcement-statistics/hide-list
// @Summary Get announcement statistics hide list
// @Description Get the full list of hidden usernames for announcement statistics
// @Tags statistics
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /admin/announcement-statistics/hide-list [get]
func (h *Handler) GetAnnouncementStatisticsHideList(c *gin.Context) {
	ctx := context.Background()
	key := h.getAnnouncementStatsHideSetKey()
	members, err := h.redisService.GetClient().SMembers(ctx, key).Result()
	if err != nil {
		log.Error().Err(err).Str("key", key).Msg("Failed to fetch hide set")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to fetch list"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"list": members})
}

// GetAnnouncementStatistics handles GET /api/announcement-statistics
// @Summary Get announcement statistics
// @Description Get statistics for tweets with source_list_type="Projects" and ai_judgment="YES" grouped by Twitter user. Optional time filtering with start_time and end_time query parameters (Unix timestamps). Optional sorting with sort_field and sort_direction parameters.
// @Tags statistics
// @Accept json
// @Produce json
// @Param start_time query int false "Start time (Unix timestamp, inclusive)" example(1640995200)
// @Param end_time query int false "End time (Unix timestamp, inclusive)" example(1672531199)
// @Param sort_field query string false "Field to sort by" Enums(product_updates, business_data, ecosystem_partnership, profit_opportunity, industry_events, others, total, days_since_last_tweet)
// @Param sort_direction query string false "Sort direction" Enums(asc, desc)
// @Success 200 {array} UserAnnouncementStatsResponse
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /announcement-statistics [get]
func (h *Handler) GetAnnouncementStatistics(c *gin.Context) {
	log.Info().Msg("Getting announcement statistics")

	// Parse optional time parameters
	var startTime, endTime *int64

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTimeVal, err := strconv.ParseInt(startTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("start_time", startTimeStr).Msg("Invalid start_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_time parameter. Must be a valid Unix timestamp."})
			return
		} else {
			startTime = &startTimeVal
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTimeVal, err := strconv.ParseInt(endTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("end_time", endTimeStr).Msg("Invalid end_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_time parameter. Must be a valid Unix timestamp."})
			return
		} else {
			endTime = &endTimeVal
		}
	}

	// Validate time range if both parameters are provided
	if startTime != nil && endTime != nil && *startTime > *endTime {
		log.Error().Int64("start_time", *startTime).Int64("end_time", *endTime).Msg("Invalid time range: start_time is greater than end_time")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid time range: start_time must be less than or equal to end_time."})
		return
	}

	// Parse and validate sorting parameters
	sortField := c.Query("sort_field")
	sortDirection := c.Query("sort_direction")

	if sortField == "" {
		sortField = "total"
	}
	if sortDirection == "" {
		sortDirection = "desc"
	}

	// Validate sort_field if provided
	validSortFields := map[string]bool{
		"product_updates":       true,
		"business_data":         true,
		"ecosystem_partnership": true,
		"profit_opportunity":    true,
		"industry_events":       true,
		"others":                true,
		"total":                 true,
		"days_since_last_tweet": true,
	}
	if !validSortFields[sortField] {
		log.Error().Str("sort_field", sortField).Msg("Invalid sort_field parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sort_field parameter. Valid values are: product_updates, business_data, ecosystem_partnership, profit_opportunity, industry_events, others, total, days_since_last_tweet."})
		return
	}

	// Validate sort_direction if provided
	if sortDirection != "asc" && sortDirection != "desc" {
		log.Error().Str("sort_direction", sortDirection).Msg("Invalid sort_direction parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sort_direction parameter. Valid values are: asc, desc."})
		return
	}

	// Get announcement statistics from the database
	userStats, err := h.twitterService.GetAnnouncementStatistics(startTime, endTime)
	if err != nil {
		log.Error().Err(err).Msg("Error getting announcement statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting announcement statistics"})
		return
	}

	// Load hide list from Redis and build a lookup map for filtering
	ctx := context.Background()
	hideKey := h.getAnnouncementStatsHideSetKey()
	hideMembers, err := h.redisService.GetClient().SMembers(ctx, hideKey).Result()
	if err != nil {
		log.Warn().Err(err).Str("key", hideKey).Msg("Failed to load hide set; proceeding without filtering")
	}
	hidden := make(map[string]struct{}, len(hideMembers))
	for _, m := range hideMembers {
		hidden[strings.ToLower(strings.TrimSpace(m))] = struct{}{}
	}

	// Convert to response format
	response := make([]UserAnnouncementStatsResponse, 0, len(userStats))
	for _, userStat := range userStats {
		// Filter out users present in the hide set
		if _, skip := hidden[strings.ToLower(userStat.ScreenName)]; skip {
			continue
		}
		// Get tags from cached collections data based on screen_name
		tags := h.getTagsFromCacheByTwitterUsername(userStat.ScreenName)

		response = append(response, UserAnnouncementStatsResponse{
			TwitterUser: TwitterUserInfo{
				UserID:          userStat.UserID,
				ScreenName:      userStat.ScreenName,
				Name:            userStat.Name,
				ProfileImageURL: userStat.ProfileImageURL,
				Tags:            tags,
			},
			ProductUpdatesCount:       userStat.ProductUpdatesCount,
			BusinessDataCount:         userStat.BusinessDataCount,
			EcosystemPartnershipCount: userStat.EcosystemPartnershipCount,
			ProfitOpportunityCount:    userStat.ProfitOpportunityCount,
			IndustryEventsCount:       userStat.IndustryEventsCount,
			OthersCount:               userStat.OthersCount,
			TotalAnnouncementsCount:   userStat.TotalAnnouncementsCount,
			DaysSinceLastTweet:        &userStat.DaysSinceLastTweet,
		})
	}

	// Sort response according to parameters
	sort.Slice(response, func(i, j int) bool {
		if sortField == "days_since_last_tweet" {
			var vi, vj int
			if response[i].DaysSinceLastTweet != nil {
				vi = *response[i].DaysSinceLastTweet
			}
			if response[j].DaysSinceLastTweet != nil {
				vj = *response[j].DaysSinceLastTweet
			}
			if sortDirection == "asc" {
				return vi < vj
			}
			return vi > vj
		}

		var vi, vj int64
		switch sortField {
		case "product_updates":
			vi = response[i].ProductUpdatesCount
			vj = response[j].ProductUpdatesCount
		case "business_data":
			vi = response[i].BusinessDataCount
			vj = response[j].BusinessDataCount
		case "ecosystem_partnership":
			vi = response[i].EcosystemPartnershipCount
			vj = response[j].EcosystemPartnershipCount
		case "profit_opportunity":
			vi = response[i].ProfitOpportunityCount
			vj = response[j].ProfitOpportunityCount
		case "industry_events":
			vi = response[i].IndustryEventsCount
			vj = response[j].IndustryEventsCount
		case "others":
			vi = response[i].OthersCount
			vj = response[j].OthersCount
		case "total":
			vi = response[i].TotalAnnouncementsCount
			vj = response[j].TotalAnnouncementsCount
		}
		if sortDirection == "asc" {
			return vi < vj
		}
		return vi > vj
	})

	// Return JSON response
	c.JSON(http.StatusOK, response)
}

// AnnouncementStatisticsTotalResponse represents the total announcement statistics response
type AnnouncementStatisticsTotalResponse struct {
	TotalAnnouncements int64   `json:"total_announcements"`
	ChangePercentage   float64 `json:"change_percentage_24h"`
	PreviousDayTotal   int64   `json:"previous_day_total"`
}

// GetAnnouncementStatisticsTotal handles GET /api/announcement-statistics/total
// @Summary Get total announcement statistics
// @Description Get total announcement count and 24-hour change percentage for all projects
// @Tags statistics
// @Accept json
// @Produce json
// @Success 200 {object} AnnouncementStatisticsTotalResponse
// @Failure 500 {object} map[string]string
// @Router /announcement-statistics/total [get]
func (h *Handler) GetAnnouncementStatisticsTotal(c *gin.Context) {
	log.Info().Msg("Getting total announcement statistics")

	// Get total announcement statistics from the service
	totalStats, err := h.twitterService.GetAnnouncementStatisticsTotal()
	if err != nil {
		log.Error().Err(err).Msg("Error getting total announcement statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting total announcement statistics"})
		return
	}

	// Return JSON response
	c.JSON(http.StatusOK, totalStats)
}

// extractImagesFromTweet extracts image URLs from a tweet
func extractImagesFromTweet(tweetJSON db.JSONB) []string {
	images := []string{}
	// Use a map to track unique URLs
	uniqueURLs := make(map[string]struct{})

	// Check if entities and media exist
	entities, ok := tweetJSON["entities"].(map[string]interface{})
	if !ok {
		return images
	}

	media, ok := entities["media"].([]interface{})
	if !ok {
		return images
	}

	// Extract image URLs
	for _, m := range media {
		mediaObj, ok := m.(map[string]interface{})
		if !ok {
			continue
		}

		mediaURL, ok := mediaObj["media_url_https"].(string)
		if !ok {
			continue
		}

		// Only add if we haven't seen this URL before
		if _, exists := uniqueURLs[mediaURL]; !exists {
			uniqueURLs[mediaURL] = struct{}{}
			images = append(images, mediaURL)
		}
	}

	return images
}

// getCaByUserName calls the external API to get CA by username
func (h *Handler) getCaByUserName(userName string) (*GetCaByUserNameResponse, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	apiURL := fmt.Sprintf("https://api.scattering.io/api/v3/tokens/by_tweet_username?twitter_username=%s", url.QueryEscape(userName))

	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call CA by username API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	var result GetCaByUserNameResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// getTokenInfo calls the external API to get token information
func (h *Handler) getTokenInfo(address string) (*GetTradeTokenInfoResponse, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	apiURL := fmt.Sprintf("https://scattering.io/api/collections?page=1&page_size=10&sort_field=total_volume_in_24hours&chain_id=&sort_direction=desc&name_like=%s", url.QueryEscape(address))

	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to call token info API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code: %d", resp.StatusCode)
	}

	var result GetTradeTokenInfoResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// getTokenInfoFromCache searches for token information in the cached collections data
func (h *Handler) getTokenInfoFromCache(address string) (*GetTradeTokenInfoResponse, error) {
	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get cached collections, falling back to API")
		// Fallback to API call if cache is not available
		return h.getTokenInfo(address)
	}

	// Search for matching tokens by address (case-insensitive)
	var matchingTokens []TradeTokenInfo
	searchAddress := strings.ToLower(address)

	for _, collection := range collections {
		if strings.ToLower(collection.Address) == searchAddress ||
			strings.Contains(strings.ToLower(collection.Address), searchAddress) ||
			strings.Contains(strings.ToLower(collection.Name), searchAddress) ||
			strings.Contains(strings.ToLower(collection.Symbol), searchAddress) {
			matchingTokens = append(matchingTokens, collection)
		}
	}

	// If no matches found in cache, fallback to API
	if len(matchingTokens) == 0 {
		log.Debug().
			Str("address", address).
			Msg("No matching tokens found in cache, falling back to API")
		return h.getTokenInfo(address)
	}

	// Sort by total volume in 24 hours (descending) to match API behavior
	sort.Slice(matchingTokens, func(i, j int) bool {
		vol1, _ := strconv.ParseFloat(matchingTokens[i].TotalVolumeIn24Hours, 64)
		vol2, _ := strconv.ParseFloat(matchingTokens[j].TotalVolumeIn24Hours, 64)
		return vol1 > vol2
	})

	// Limit to 10 results to match API behavior
	if len(matchingTokens) > 10 {
		matchingTokens = matchingTokens[:10]
	}

	// Create response in the same format as the API
	response := &GetTradeTokenInfoResponse{
		Code: 200,
		Data: struct {
			List       []TradeTokenInfo `json:"list"`
			Page       int              `json:"page"`
			PageSize   int              `json:"page_size"`
			TotalCount int              `json:"total_count"`
		}{
			List:       matchingTokens,
			Page:       1,
			PageSize:   len(matchingTokens),
			TotalCount: len(matchingTokens),
		},
		Msg: "success",
	}

	log.Debug().
		Str("address", address).
		Int("matches_found", len(matchingTokens)).
		Msg("Found matching tokens in cache")

	return response, nil
}

type GetCaByUserNameResponse struct {
	Code int `json:"code"`
	Data struct {
		Data []struct {
			Address string `json:"address"`
			ChainId int    `json:"chain_id"`
		} `json:"data"`
		TotalCount int `json:"total_count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type GetTradeTokenInfoResponse struct {
	Code int `json:"code"`
	Data struct {
		List       []TradeTokenInfo `json:"list"`
		Page       int              `json:"page"`
		PageSize   int              `json:"page_size"`
		TotalCount int              `json:"total_count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type TradeTokenInfo struct {
	ChainId           int       `json:"chain_id"`
	Address           string    `json:"address"`
	Name              string    `json:"name"`
	Symbol            string    `json:"symbol"`
	Decimals          int       `json:"decimals"`
	TotalSupply       string    `json:"total_supply"`
	MarketCap         string    `json:"market_cap"`
	Status            int       `json:"status"`
	Type              int       `json:"type"`
	LogoUrl           string    `json:"logo_url"`
	CreationDate      time.Time `json:"creation_date"`
	IsVerified        bool      `json:"is_verified"`
	Slug              string    `json:"slug"`
	Description       string    `json:"description"`
	BannerUrl         string    `json:"banner_url"`
	MobileBannerUrl   string    `json:"mobile_banner_url"`
	DiscordUrl        string    `json:"discord_url"`
	TelegramUrl       string    `json:"telegram_url"`
	CreatorXUsername  string    `json:"creator_x_username"`
	InstagramUsername string    `json:"instagram_username"`
	MediumUrl         string    `json:"medium_url"`
	TiktokUrl         string    `json:"tiktok_url"`
	RedditUrl         string    `json:"reddit_url"`
	WarpcastUrl       string    `json:"warpcast_url"`
	CoinmarketcapUrl  string    `json:"coinmarketcap_url"`
	CoingeckoUrl      string    `json:"coingecko_url"`
	GithubUrl         string    `json:"github_url"`
	Profile           string    `json:"profile"`
	ResearchReport    string    `json:"research_report"`
	AiReport          string    `json:"ai_report"`
	TwitterUsername   string    `json:"twitter_username"`
	TwitterUserId     string    `json:"twitter_user_id"`
	Tags              []struct {
		Name  string `json:"name"`
		Color string `json:"color"`
		Rank  int    `json:"rank"`
		Type  int    `json:"type"`
	} `json:"tags"`
	PriceInUsd              string `json:"price_in_usd"`
	TotalLiquidity          string `json:"total_liquidity"`
	Rank                    int    `json:"rank"`
	IsWatched               bool   `json:"is_watched"`
	PriceChangeIn24Hours    string `json:"price_change_in_24hours"`
	PriceChangeIn6Hours     string `json:"price_change_in_6hours"`
	PriceChangeIn1Hours     string `json:"price_change_in_1hours"`
	TotalVolumeIn24Hours    string `json:"total_volume_in_24hours"`
	TotalVolumeIn6Hours     string `json:"total_volume_in_6hours"`
	TotalVolumeIn1Hours     string `json:"total_volume_in_1hours"`
	TotalTxCount24Hours     string `json:"total_tx_count_24hours"`
	TotalBuyCount24Hours    string `json:"total_buy_count_24hours"`
	TotalSellCount24Hours   string `json:"total_sell_count_24hours"`
	TotalMakersCount24Hours string `json:"total_makers_count_24hours"`
	TotalBuyerCount24Hours  string `json:"total_buyer_count_24hours"`
	TotalSellerCount24Hours string `json:"total_seller_count_24hours"`
	ProjectUrl              string `json:"project_url"`
	TwitterScore            string `json:"twitter_score"`
	FollowersCount          int    `json:"followers_count"`
	InfluencersCount        int    `json:"influencers_count"`
	ProjectsCount           int    `json:"projects_count"`
	VentureCapitalsCount    int    `json:"venture_capitals_count"`
	Top20Followers          []struct {
		Name     string `json:"name"`
		Avatar   string `json:"avatar"`
		Username string `json:"username"`
	} `json:"top_20_followers"`
}

// GetTelegramNotificationStats returns statistics about Telegram notifications
// @Summary Get Telegram notification statistics
// @Description Returns statistics about Telegram notifications including total sent, duplicates prevented, etc.
// @Tags notifications
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Notification statistics"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /telegram-notification-stats [get]
func (h *Handler) GetTelegramNotificationStats(c *gin.Context) {
	stats, err := h.twitterService.GetTelegramNotificationStats()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Telegram notification statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get notification statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// ExcludedTwitterUserRequest represents a request to add/remove an excluded Twitter user
type ExcludedTwitterUserRequest struct {
	TwitterUserName string `json:"twitter_user_name" binding:"required"`
	Reason          string `json:"-"`
	ExcludedBy      string `json:"-"`
}

// AddExcludedTwitterUser godoc
// @Summary      Add excluded Twitter user
// @Description  Adds a Twitter user to the exclusion list for statistics and API returns
// @Tags         excluded-users
// @Accept       json
// @Produce      json
// @Param        request body ExcludedTwitterUserRequest true "Excluded user request"
// @Success      201     {object}  map[string]string
// @Failure      400     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /excluded-twitter-users [post]
func (h *Handler) AddExcludedTwitterUser(c *gin.Context) {
	var req ExcludedTwitterUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Add excluded Twitter user
	err := h.twitterService.AddExcludedTwitterUser(req.TwitterUserName, req.Reason, req.ExcludedBy)
	if err != nil {
		log.Error().Err(err).Str("twitter_user_name", req.TwitterUserName).Msg("Error adding excluded Twitter user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error adding excluded Twitter user"})
		return
	}

	log.Info().Str("twitter_user_name", req.TwitterUserName).Str("excluded_by", req.ExcludedBy).Msg("Twitter user added to exclusion list")
	c.JSON(http.StatusCreated, gin.H{"status": "success", "message": "Twitter user added to exclusion list"})
}

// RemoveExcludedTwitterUser godoc
// @Summary      Remove excluded Twitter user
// @Description  Removes a Twitter user from the exclusion list
// @Tags         excluded-users
// @Accept       json
// @Produce      json
// @Param        twitter_user_name path string true "Twitter user name"
// @Success      200     {object}  map[string]string
// @Failure      500     {object}  map[string]string
// @Router       /excluded-twitter-users/{twitter_user_name} [delete]
func (h *Handler) RemoveExcludedTwitterUser(c *gin.Context) {
	twitterUserName := c.Param("twitter_user_name")
	if twitterUserName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Twitter user name is required"})
		return
	}

	// Remove excluded Twitter user
	err := h.twitterService.RemoveExcludedTwitterUser(twitterUserName)
	if err != nil {
		log.Error().Err(err).Str("twitter_user_name", twitterUserName).Msg("Error removing excluded Twitter user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error removing excluded Twitter user"})
		return
	}

	log.Info().Str("twitter_user_name", twitterUserName).Msg("Twitter user removed from exclusion list")
	c.JSON(http.StatusOK, gin.H{"status": "success", "message": "Twitter user removed from exclusion list"})
}

// GetExcludedTwitterUsers godoc
// @Summary      Get excluded Twitter users
// @Description  Retrieves all excluded Twitter users
// @Tags         excluded-users
// @Accept       json
// @Produce      json
// @Success      200     {array}   db.ExcludedTwitterUser
// @Failure      500     {object}  map[string]string
// @Router       /excluded-twitter-users [get]
func (h *Handler) GetExcludedTwitterUsers(c *gin.Context) {
	excludedUsers, err := h.twitterService.GetExcludedTwitterUsers()
	if err != nil {
		log.Error().Err(err).Msg("Error getting excluded Twitter users")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error getting excluded Twitter users"})
		return
	}

	c.JSON(http.StatusOK, excludedUsers)
}

// StartScheduler starts the cron scheduler for periodic tasks
func (h *Handler) StartScheduler() {
	if h.scheduler == nil {
		log.Error().Msg("Scheduler is not initialized")
		return
	}

	// Execute collections task immediately on startup
	h.collectionsScheduledTask()

	// Add collections cache update job - runs every 5 days at 2 AM
	_, err := h.scheduler.AddFunc("0 2 */5 * *", h.collectionsScheduledTask)
	if err != nil {
		log.Error().Err(err).Msg("Failed to add collections cache update job")
		return
	}

	// Add Dune data fetch job if Dune service is available - runs daily at 10 AM UTC+8
	// Execute Dune task immediately on startup
	h.duneScheduledTask()

	_, err = h.scheduler.AddFunc("0 0 * * *", h.duneScheduledTask)
	if err != nil {
		log.Error().Err(err).Msg("Failed to add Dune data fetch job")
		return
	}

	_, err = h.scheduler.AddFunc("0 8 * * *", h.duneScheduledTask)
	if err != nil {
		log.Error().Err(err).Msg("Failed to add Dune data fetch job")
		return
	}

	_, err = h.scheduler.AddFunc("0 17 * * *", h.duneScheduledTask)
	if err != nil {
		log.Error().Err(err).Msg("Failed to add Dune data fetch job")
		return
	}

	// Start the scheduler
	h.scheduler.Start()
	log.Info().Msg("Cron scheduler started with collections cache update job and Dune data fetch job")
}

// StopScheduler stops the cron scheduler
func (h *Handler) StopScheduler() {
	if h.scheduler != nil {
		h.scheduler.Stop()
		log.Info().Msg("Cron scheduler stopped")
	}
}

// duneScheduledTask is the scheduled task that fetches Dune data
func (h *Handler) duneScheduledTask() {
	if h.duneService != nil {
		h.duneService.FetchAllDuneData()
	}
}

// collectionsScheduledTask is the scheduled task that fetches and caches collections data
func (h *Handler) collectionsScheduledTask() {
	log.Info().Msg("Starting scheduled collections cache update task")

	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Info().Dur("duration", duration).Msg("Completed scheduled collections cache update task")
	}()

	// Fetch collections data from API
	collections, err := h.fetchCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch collections data")
		return
	}

	// Filter collections with tags
	filteredCollections := make([]TradeTokenInfo, 0)
	for _, collection := range collections {
		if len(collection.Tags) > 0 {
			filteredCollections = append(filteredCollections, collection)
		}
	}

	log.Info().
		Int("total_collections", len(collections)).
		Int("filtered_collections", len(filteredCollections)).
		Msg("Filtered collections by tags")

	// Cache the filtered data in Redis
	if err := h.cacheCollectionsData(filteredCollections); err != nil {
		log.Error().Err(err).Msg("Failed to cache collections data")
		return
	}

	// Save collection tags to database
	// Convert TradeTokenInfo to interface{} for database operation
	collectionsInterface := make([]interface{}, len(filteredCollections))
	for i, collection := range filteredCollections {
		// Convert struct to map for easier processing in database layer
		collectionMap := map[string]interface{}{
			"twitter_username": collection.TwitterUsername,
			"tags":             make([]interface{}, len(collection.Tags)),
		}

		// Convert tags to interface{}
		for j, tag := range collection.Tags {
			tagMap := map[string]interface{}{
				"name": tag.Name,
			}
			collectionMap["tags"].([]interface{})[j] = tagMap
		}

		collectionsInterface[i] = collectionMap
	}

	if err := h.twitterService.SaveCollectionTags(collectionsInterface); err != nil {
		log.Error().Err(err).Msg("Failed to save collection tags to database")
		// Don't return here, continue with Redis caching
	} else {
		log.Info().
			Int("collections_processed", len(filteredCollections)).
			Msg("Successfully saved collection tags to database")
	}

	log.Info().
		Int("cached_collections", len(filteredCollections)).
		Msg("Successfully cached collections data")
}

// fetchCollectionsData fetches all collections data from scattering.io API with pagination
func (h *Handler) fetchCollectionsData() ([]TradeTokenInfo, error) {
	var allCollections []TradeTokenInfo
	page := 1
	pageSize := 100

	client := &http.Client{Timeout: 30 * time.Second}

	for {
		// Build API URL with pagination and sorting parameters
		apiURL := fmt.Sprintf("https://scattering.io/api/collections?page=%d&page_size=%d&sort_field=total_volume_in_24hours&sort_direction=desc",
			page, pageSize)

		log.Debug().
			Str("url", apiURL).
			Int("page", page).
			Msg("Fetching collections page")

		// Make HTTP request
		resp, err := client.Get(apiURL)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch collections page %d: %w", page, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("API returned status code %d for page %d", resp.StatusCode, page)
		}

		// Parse response
		var collectionsResp GetTradeTokenInfoResponse
		if err := json.NewDecoder(resp.Body).Decode(&collectionsResp); err != nil {
			return nil, fmt.Errorf("failed to decode response for page %d: %w", page, err)
		}

		// Check API response code
		if collectionsResp.Code != 0 {
			return nil, fmt.Errorf("API returned error code %d: %s", collectionsResp.Code, collectionsResp.Msg)
		}

		// Add collections from this page
		allCollections = append(allCollections, collectionsResp.Data.List...)

		log.Debug().
			Int("page", page).
			Int("page_collections", len(collectionsResp.Data.List)).
			Int("total_so_far", len(allCollections)).
			Int("total_count", collectionsResp.Data.TotalCount).
			Msg("Fetched collections page")

		// Check if we've fetched all pages
		if len(collectionsResp.Data.List) < pageSize || len(allCollections) >= collectionsResp.Data.TotalCount {
			break
		}

		page++

		// Add a small delay between requests to be respectful to the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Info().
		Int("total_collections", len(allCollections)).
		Int("total_pages", page).
		Msg("Successfully fetched all collections data")

	return allCollections, nil
}

// cacheCollectionsData caches the collections data in Redis with 7-day expiration
func (h *Handler) cacheCollectionsData(collections []TradeTokenInfo) error {
	if h.redisService == nil {
		return fmt.Errorf("redis service is not available")
	}

	// Serialize collections to JSON
	data, err := json.Marshal(collections)
	if err != nil {
		return fmt.Errorf("failed to marshal collections data: %w", err)
	}

	// Get Redis client
	client := h.redisService.GetClient()
	ctx := context.Background()

	// Cache key with versioned prefix
	cacheKey := "v2_collections_cache"

	// Set cache with 7-day expiration
	expiration := 7 * 24 * time.Hour
	err = client.Set(ctx, cacheKey, data, expiration).Err()
	if err != nil {
		return fmt.Errorf("failed to cache collections data: %w", err)
	}

	log.Info().
		Str("cache_key", cacheKey).
		Dur("expiration", expiration).
		Int("data_size_bytes", len(data)).
		Msg("Successfully cached collections data in Redis")

	return nil
}

// getCachedCollectionsData retrieves collections data from Redis cache
func (h *Handler) getCachedCollectionsData() ([]TradeTokenInfo, error) {
	if h.redisService == nil {
		return nil, fmt.Errorf("redis service is not available")
	}

	// Get Redis client
	client := h.redisService.GetClient()
	ctx := context.Background()

	// Cache key with versioned prefix
	cacheKey := "v2_collections_cache"

	// Get cached data
	cachedData, err := client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, fmt.Errorf("collections cache not found")
		}
		return nil, fmt.Errorf("failed to retrieve collections cache: %w", err)
	}

	// Deserialize collections from JSON
	var collections []TradeTokenInfo
	if err := json.Unmarshal([]byte(cachedData), &collections); err != nil {
		return nil, fmt.Errorf("failed to unmarshal collections data: %w", err)
	}

	log.Debug().
		Str("cache_key", cacheKey).
		Int("collections_count", len(collections)).
		Msg("Successfully retrieved collections data from Redis cache")

	return collections, nil
}

// getTagsFromCacheByTwitterUsername retrieves tags for a Twitter user from cached collections data
func (h *Handler) getTagsFromCacheByTwitterUsername(screenName string) []string {
	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Debug().Err(err).Str("screen_name", screenName).Msg("Failed to get cached collections for tags lookup")
		return []string{}
	}

	// Collect unique tags for this Twitter user
	tagSet := make(map[string]bool)
	var tags []string

	// Search for collections associated with this Twitter username
	for _, collection := range collections {
		// Match by Twitter username (case-insensitive)
		if strings.EqualFold(collection.TwitterUsername, screenName) {
			// Add all tags from this collection
			for _, tag := range collection.Tags {
				if tag.Name != "" && !tagSet[tag.Name] {
					tagSet[tag.Name] = true
					tags = append(tags, tag.Name)
				}
			}
		}
	}

	log.Debug().
		Str("screen_name", screenName).
		Int("tags_count", len(tags)).
		Strs("tags", tags).
		Msg("Retrieved tags from cache for Twitter user")

	return tags
}

// TriggerCollectionsCacheUpdate manually triggers the collections cache update
// This is useful for testing or manual cache refresh
// @Summary Manually trigger collections cache update
// @Description Triggers the collections cache update task manually for testing purposes
// @Tags system
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /trigger-collections-cache [post]
func (h *Handler) TriggerCollectionsCacheUpdate(c *gin.Context) {
	log.Info().Msg("Manual collections cache update triggered")

	// Run the scheduled task in a goroutine to avoid blocking the HTTP request
	go h.collectionsScheduledTask()

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Collections cache update task triggered",
	})
}

// GetCollectionTags returns all unique collection tags
// @Summary Get all collection tags
// @Description Returns a list of all unique collection tags from cached collections
// @Tags collections
// @Accept json
// @Produce json
// @Success 200 {object} []string "Collection tags list"
// @Failure 500 {object} map[string]string "Internal server error"
// @Router /collection-tags [get]
func (h *Handler) GetCollectionTags(c *gin.Context) {
	log.Info().Msg("Getting collection tags")

	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get cached collections data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get collections data"})
		return
	}

	// Extract unique tags
	tagsMap := make(map[string]string)

	for _, collection := range collections {
		for _, tag := range collection.Tags {
			tagsMap[tag.Name] = tag.Name
		}
	}

	// Convert map to slice
	tags := make([]string, 0, len(tagsMap))

	for _, tag := range tagsMap {
		tags = append(tags, tag)
	}

	c.JSON(http.StatusOK, tags)
}

// GetCachedCollections returns collections data from Redis cache
// @Summary Get cached collections data
// @Description Retrieves collections data from Redis cache with tags information
// @Tags collections
// @Accept json
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 100)"
// @Success 200 {object} GetTradeTokenInfoResponse
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /cached-collections [get]
func (h *Handler) GetCachedCollections(c *gin.Context) {
	log.Info().Msg("Getting cached collections data")

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "100")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 1000 {
		pageSize = 100
	}

	// Get cached collections data
	collections, err := h.getCachedCollectionsData()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get cached collections data")
		if err.Error() == "collections cache not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Collections cache not found. Please trigger cache update first.",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to retrieve cached collections data",
			})
		}
		return
	}

	// Calculate pagination
	totalCount := len(collections)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalCount {
		// Return empty result if page is beyond available data
		response := GetTradeTokenInfoResponse{
			Code: 200,
			Data: struct {
				List       []TradeTokenInfo `json:"list"`
				Page       int              `json:"page"`
				PageSize   int              `json:"page_size"`
				TotalCount int              `json:"total_count"`
			}{
				List:       []TradeTokenInfo{},
				Page:       page,
				PageSize:   pageSize,
				TotalCount: totalCount,
			},
			Msg: "success",
		}
		c.JSON(http.StatusOK, response)
		return
	}

	if endIndex > totalCount {
		endIndex = totalCount
	}

	// Get paginated data
	paginatedCollections := collections[startIndex:endIndex]

	// Create response
	response := GetTradeTokenInfoResponse{
		Code: 200,
		Data: struct {
			List       []TradeTokenInfo `json:"list"`
			Page       int              `json:"page"`
			PageSize   int              `json:"page_size"`
			TotalCount int              `json:"total_count"`
		}{
			List:       paginatedCollections,
			Page:       page,
			PageSize:   pageSize,
			TotalCount: totalCount,
		},
		Msg: "success",
	}

	log.Info().
		Int("total_count", totalCount).
		Int("page", page).
		Int("page_size", pageSize).
		Int("returned_count", len(paginatedCollections)).
		Msg("Successfully returned cached collections data")

	c.JSON(http.StatusOK, response)
}

// Dune service handlers

// CategoryQueryMapping represents a mapping between category ID and dune query ID
// DuneBindingRequest represents a request to create/update a Dune Twitter binding
// @Description Request body for creating or updating a Dune Twitter binding with category-query mappings
type DuneBindingRequest struct {
	CategoryQueries   []db.CategoryQueryMapping `json:"category_queries" binding:"required" description:"Array of category-query mappings"`
	TwitterUserName   string                    `json:"twitter_user_name" binding:"required" example:"elonmusk" description:"Twitter username"`
	ChainNames        []string                  `json:"chain_names" example:"base,bsc" description:"Comma-separated chain names"`
	ProjectName       string                    `json:"project_name" example:"Elon Musk" description:"Project name"`
	ProjectLogo       string                    `json:"project_logo" example:"https://pbs.twimg.com/profile_images/1637832152000000000/1637832152000000000.jpg" description:"Project logo URL"`
	ContractAddresses []string                  `json:"contract_addresses" example:"0x1234567890123456789012345678901234567890" description:"Comma-separated contract addresses"`
	Tags              []string                  `json:"tags" example:"nft,twitter" description:"Comma-separated tags"`
}

// UnmarshalJSON implements custom JSON unmarshaling to support both chain_names and chain_ids
func (d *DuneBindingRequest) UnmarshalJSON(data []byte) error {
	// Define a temporary struct with all fields
	type Alias DuneBindingRequest
	aux := &struct {
		ChainIDs []string `json:"chain_ids"` // Alternative field name
		*Alias
	}{
		Alias: (*Alias)(d),
	}

	// First try to unmarshal normally
	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}

	// If chain_ids was provided instead of chain_names, use it
	if len(aux.ChainIDs) > 0 && len(d.ChainNames) == 0 {
		d.ChainNames = aux.ChainIDs
	}

	return nil
}

// validateCategoriesExist validates that all provided category IDs exist in the database
func (h *Handler) validateCategoriesExist(categoryIDs []int64) error {
	for _, categoryID := range categoryIDs {
		_, err := h.duneService.GetCategoryByID(categoryID)
		if err != nil {
			return fmt.Errorf("category with ID %d not found: %w", categoryID, err)
		}
	}
	return nil
}

// categoriesChanged checks if the category associations have changed
func (h *Handler) categoriesChanged(currentCategories []db.DuneCategory, newMappings []db.CategoryQueryMapping) bool {
	// Extract category IDs from new mappings (excluding CategoryID 0 which means no category)
	newCategoryIDs := make(map[int64]bool)
	for _, mapping := range newMappings {
		if mapping.CategoryID > 0 {
			newCategoryIDs[mapping.CategoryID] = true
		}
	}

	// Extract current category IDs
	currentCategoryIDs := make(map[int64]bool)
	for _, category := range currentCategories {
		currentCategoryIDs[category.ID] = true
	}

	// Check if lengths are different
	if len(newCategoryIDs) != len(currentCategoryIDs) {
		return true
	}

	// Check if all new category IDs exist in current
	for newID := range newCategoryIDs {
		if !currentCategoryIDs[newID] {
			return true
		}
	}

	return false
}

// CreateDuneBinding creates new Dune Twitter bindings with category associations
// @Summary Create new Dune Twitter bindings
// @Description Create new bindings between Dune queries and a Twitter user with category associations
// @Tags dune
// @Accept json
// @Produce json
// @Param request body DuneBindingRequest true "Dune binding request with category-query mappings"
// @Success 201 {object} map[string]interface{} "Bindings created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 500 {object} map[string]interface{} "Failed to create bindings"
// @Router /dune/bindings [post]
func (h *Handler) CreateDuneBinding(c *gin.Context) {
	var req DuneBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	// Validate input: CategoryQueries must be provided
	if len(req.CategoryQueries) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "category_queries is required"})
		return
	}

	// Validate all categories exist
	categoryIDs := make([]int64, 0)
	for _, mapping := range req.CategoryQueries {
		if mapping.CategoryID > 0 {
			categoryIDs = append(categoryIDs, mapping.CategoryID)
		}
	}

	if len(categoryIDs) > 0 {
		if err := h.validateCategoriesExist(categoryIDs); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid categories: " + err.Error()})
			return
		}
	}

	// Get project info
	var projectName, projectLogo string
	if req.ProjectLogo != "" {
		projectLogo = req.ProjectLogo
	}
	if req.ProjectName != "" {
		projectName = req.ProjectName
	}

	if projectLogo == "" || projectName == "" {
		// If project info is not provided, try to fetch it
		tokenInfo, err := h.fetchProjectInfoByTwitterUser(req.TwitterUserName)
		if err != nil {
			log.Warn().Err(err).Str("twitter_user_name", req.TwitterUserName).
				Msg("Failed to fetch project info, proceeding with empty values")
		} else {
			projectName = tokenInfo.Name
			projectLogo = tokenInfo.LogoUrl
		}
	}

	if projectLogo == "" || projectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project logo or name is required"})
		return
	}

	// Create bindings for each category-query mapping
	createdBindings := make([]*db.DuneTwitterBinding, 0, len(req.CategoryQueries))
	queryIDs := make([]string, 0, len(req.CategoryQueries))

	for _, mapping := range req.CategoryQueries {
		binding := &db.DuneTwitterBinding{
			DuneQueryID:       mapping.DuneQueryID,
			TwitterUserName:   req.TwitterUserName,
			ChainIDs:          req.ChainNames,
			ProjectName:       projectName,
			ProjectLogo:       projectLogo,
			ContractAddresses: req.ContractAddresses,
			Tags:              req.Tags,
		}

		if err := h.duneService.CreateBinding(binding); err != nil {
			log.Error().Err(err).Str("query_id", mapping.DuneQueryID).Msg("Failed to create Dune binding")
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create binding for query %s: %s", mapping.DuneQueryID, err.Error())})
			return
		}

		// Associate with category if CategoryID > 0
		if mapping.CategoryID > 0 {
			if err := h.duneService.AssociateBindingWithCategories(binding.ID, []int64{mapping.CategoryID}); err != nil {
				log.Error().Err(err).Int64("binding_id", binding.ID).Int64("category_id", mapping.CategoryID).
					Msg("Failed to associate binding with category")
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to associate binding with category: %s", err.Error())})
				return
			}
		}

		createdBindings = append(createdBindings, binding)
		queryIDs = append(queryIDs, mapping.DuneQueryID)
	}

	// Fetch Dune query data asynchronously for all created bindings
	go func() {
		for _, queryID := range queryIDs {
			if err := h.duneService.FetchDuneQueryData(queryID); err != nil {
				log.Warn().Err(err).Str("query_id", queryID).Msg("Failed to fetch Dune query data after binding creation")
			}
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"status":    "success",
		"message":   fmt.Sprintf("Created %d bindings successfully", len(createdBindings)),
		"bindings":  createdBindings,
		"query_ids": queryIDs,
	})
}

// GetDuneBindings retrieves all Dune Twitter bindings with pagination
// @Summary Get all Dune Twitter bindings
// @Description Retrieve all Dune Twitter bindings with pagination support
// @Tags dune
// @Accept json
// @Produce json
// @Param limit query int false "Number of bindings to return (default: 100)"
// @Param offset query int false "Number of bindings to skip (default: 0)"
// @Success 200 {array} db.DuneBindingResponse "List of Dune Twitter bindings"
// @Failure 500 {object} map[string]interface{} "Failed to get bindings"
// @Router /dune/bindings [get]
func (h *Handler) GetDuneBindings(c *gin.Context) {
	limitStr := c.Query("limit")
	offsetStr := c.Query("offset")

	limit := 100 // Default limit
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	offset := 0 // Default offset
	if offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	bindings, err := h.duneService.GetAllBindingsWithCategoryQueries(limit, offset)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Dune bindings")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bindings"})
		return
	}

	c.JSON(http.StatusOK, bindings)
}

// GetDuneBinding retrieves a specific Dune Twitter binding by ID
// @Summary Get a specific Dune Twitter binding
// @Description Retrieve a specific Dune Twitter binding by its ID with category-query mappings
// @Tags dune
// @Accept json
// @Produce json
// @Param id path int true "Binding ID"
// @Success 200 {object} db.DuneBindingResponse "Dune Twitter binding with category queries"
// @Failure 400 {object} map[string]interface{} "Invalid binding ID"
// @Failure 404 {object} map[string]interface{} "Binding not found"
// @Failure 500 {object} map[string]interface{} "Failed to get binding"
// @Router /dune/bindings/{id} [get]
func (h *Handler) GetDuneBinding(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	binding, err := h.duneService.GetBindingWithCategoryQueries(id)
	if err != nil {
		log.Error().Err(err).Int64("id", id).Msg("Failed to get Dune binding")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get binding"})
		return
	}

	if binding == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Binding not found"})
		return
	}

	c.JSON(http.StatusOK, binding)
}

// UpdateDuneBinding updates an existing Dune Twitter binding
// @Summary Update a Dune Twitter binding
// @Description Update an existing Dune Twitter binding by its ID
// @Tags dune
// @Accept json
// @Produce json
// @Param id path int true "Binding ID"
// @Param request body DuneBindingRequest true "Updated binding data"
// @Success 200 {object} map[string]interface{} "Binding updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid binding ID or request body"
// @Failure 500 {object} map[string]interface{} "Failed to update binding"
// @Router /dune/bindings/{id} [put]
func (h *Handler) UpdateDuneBinding(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	var req DuneBindingRequest
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	// Validate input: CategoryQueries must be provided
	if len(req.CategoryQueries) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "category_queries is required"})
		return
	}

	// Validate that all categories exist
	categoryIDs := make([]int64, 0)
	for _, mapping := range req.CategoryQueries {
		categoryIDs = append(categoryIDs, mapping.CategoryID)
	}
	if len(categoryIDs) > 0 {
		if err = h.validateCategoriesExist(categoryIDs); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	// Get current binding to check for changes
	currentBinding, err := h.duneService.GetBinding(id)
	if err != nil {
		log.Error().Err(err).Int64("id", id).Msg("Failed to get current binding")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get current binding"})
		return
	}
	if currentBinding == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Binding not found"})
		return
	}

	// For simplicity, use the first query ID for change detection and primary updates
	firstQueryID := req.CategoryQueries[0].DuneQueryID

	// Check if query ID and Twitter user name have changed
	queryIDChanged := currentBinding.DuneQueryID != firstQueryID
	twitterUserChanged := currentBinding.TwitterUserName != req.TwitterUserName
	chainIDsChanged := !equalStringSlices(currentBinding.ChainIDs, req.ChainNames)
	contractAddressesChanged := !equalStringSlices(currentBinding.ContractAddresses, req.ContractAddresses)
	tagsChanged := !equalStringSlices(currentBinding.Tags, req.Tags)
	projectNameChanged := currentBinding.ProjectName != req.ProjectName
	projectLogoChanged := currentBinding.ProjectLogo != req.ProjectLogo

	// Check if category associations have changed
	currentCategories, err := h.duneService.GetCategoriesForBinding(id)
	if err != nil {
		log.Error().Err(err).Int64("id", id).Msg("Failed to get current categories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get current categories"})
		return
	}
	categoriesChanged := h.categoriesChanged(currentCategories, req.CategoryQueries)

	// If nothing important has changed, skip the update
	if !queryIDChanged && !twitterUserChanged && !chainIDsChanged && !contractAddressesChanged && !tagsChanged && !projectNameChanged && !projectLogoChanged && !categoriesChanged {
		log.Info().Int64("id", id).Msg("No changes detected, skipping update")
		c.JSON(http.StatusOK, gin.H{"status": "success", "message": "No changes detected"})
		return
	}

	var projectName, projectLogo string
	if req.ProjectLogo != "" {
		projectLogo = req.ProjectLogo
	}
	if req.ProjectName != "" {
		projectName = req.ProjectName
	}

	if projectLogo == "" || projectName == "" {
		// Only fetch project info if Twitter user name has changed
		if twitterUserChanged {
			tokenInfo, err := h.fetchProjectInfoByTwitterUser(req.TwitterUserName)
			if err != nil {
				log.Warn().Err(err).Str("twitter_user_name", req.TwitterUserName).
					Msg("Failed to fetch project info during update, proceeding with empty values")
			} else {
				projectName = tokenInfo.Name
				projectLogo = tokenInfo.LogoUrl
			}
		} else {
			// Keep existing project info if Twitter user name hasn't changed
			projectName = currentBinding.ProjectName
			projectLogo = currentBinding.ProjectLogo
		}
	}

	if projectLogo == "" || projectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Project logo or name is required"})
		return
	}

	updates := map[string]interface{}{
		"dune_query_id":     firstQueryID,
		"twitter_user_name": req.TwitterUserName,
		// Convert []string to db.StringArray to ensure proper JSONB serialization
		"chain_ids":          db.StringArray(req.ChainNames),
		"project_name":       projectName,
		"project_logo":       projectLogo,
		"contract_addresses": db.StringArray(req.ContractAddresses),
		"tags":               db.StringArray(req.Tags),
	}

	if err := h.duneService.UpdateBinding(id, updates); err != nil {
		log.Error().Err(err).Int64("id", id).Msg("Failed to update Dune binding")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update binding"})
		return
	}

	// Update category associations if they have changed
	if categoriesChanged {
		if err := h.duneService.AssociateCategoriesWithBinding(id, categoryIDs); err != nil {
			log.Error().Err(err).Int64("id", id).Msg("Failed to update binding categories")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update binding categories"})
			return
		}
		log.Info().Int64("id", id).Ints64("category_ids", categoryIDs).Msg("Updated binding categories")
	}

	// Fetch Dune query data asynchronously for all queries after successful binding update
	for _, mapping := range req.CategoryQueries {
		go func(queryID string) {
			if err := h.duneService.FetchDuneQueryData(queryID); err != nil {
				log.Warn().Err(err).Str("query_id", queryID).Msg("Failed to fetch Dune query data after binding update")
			}
		}(mapping.DuneQueryID)
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// DuneFetchAllResponse represents the response for manual Dune data fetch trigger
type DuneFetchAllResponse struct {
	Status      string `json:"status" example:"success"`
	Message     string `json:"message" example:"Dune data fetch triggered successfully"`
	QueryCount  int    `json:"query_count" example:"5"`
	Duration    string `json:"duration,omitempty" example:"2.5s"`
	TriggeredAt int64  `json:"triggered_at" example:"1672531200"`
	TriggeredBy string `json:"triggered_by,omitempty" example:"manual"`
}

// TriggerDuneFetchAll manually triggers the Dune data fetch for all bindings
// @Summary Manually trigger Dune data fetch
// @Description Manually triggers the FetchAllDuneData method to fetch data for all Dune query bindings
// @Tags dune
// @Accept json
// @Produce json
// @Param X-API-Key header string false "Optional API key for access control"
// @Success 200 {object} DuneFetchAllResponse "Fetch triggered successfully"
// @Failure 500 {object} map[string]interface{} "Failed to trigger fetch"
// @Router /dune/fetch-all [post]
func (h *Handler) TriggerDuneFetchAll(c *gin.Context) {
	log.Info().
		Str("ip", c.ClientIP()).
		Str("user_agent", c.Request.UserAgent()).
		Msg("Manual Dune data fetch triggered via API")

	start := time.Now()

	// Optional: Simple API key validation (if X-API-Key header is provided)
	// This is a basic security measure - you can enhance this as needed
	apiKey := c.GetHeader("X-API-Key")
	if apiKey != "" {
		// You can add validation logic here if needed
		// For now, we just log that an API key was provided
		log.Info().Msg("API key provided for manual Dune fetch trigger")
	}

	// Get the count of unique query IDs before triggering the fetch
	var queryCount int64
	err := h.duneService.GetDB().DB.Model(&db.DuneTwitterBinding{}).
		Distinct("dune_query_id").
		Count(&queryCount).Error

	if err != nil {
		log.Error().Err(err).Msg("Failed to get query count for manual fetch trigger")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":  "Failed to get query count",
			"status": "error",
		})
		return
	}

	// Trigger the fetch in a goroutine to avoid blocking the HTTP response
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Error().Interface("panic", r).Msg("Panic occurred during manual Dune data fetch")
			}
		}()

		h.duneService.FetchAllDuneData()
	}()

	duration := time.Since(start)

	// Return immediate response
	response := DuneFetchAllResponse{
		Status:      "success",
		Message:     "Dune data fetch triggered successfully",
		QueryCount:  int(queryCount),
		Duration:    duration.String(),
		TriggeredAt: time.Now().Unix(),
		TriggeredBy: "manual",
	}

	log.Info().
		Int("query_count", int(queryCount)).
		Dur("response_time", duration).
		Msg("Manual Dune data fetch triggered successfully")

	c.JSON(http.StatusOK, response)
}

// DeleteDuneBinding deletes a Dune Twitter binding
// @Summary Delete a Dune Twitter binding
// @Description Delete a Dune Twitter binding by its ID
// @Tags dune
// @Accept json
// @Produce json
// @Param id path int true "Binding ID"
// @Success 200 {object} map[string]interface{} "Binding deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid binding ID"
// @Failure 500 {object} map[string]interface{} "Failed to delete binding"
// @Router /dune/bindings/{id} [delete]
func (h *Handler) DeleteDuneBinding(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	if err := h.duneService.DeleteBinding(id); err != nil {
		log.Error().Err(err).Int64("id", id).Msg("Failed to delete Dune binding")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete binding"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// GetDuneProjectData retrieves project data by Twitter username and time range
// @Summary Get Dune project data
// @Description Retrieve project data from Dune Analytics by Twitter username and time range
// @Tags dune
// @Accept json
// @Produce json
// @Param id path string true "Query ID"
// @Param start_time query string false "Start time (Unix timestamp)"
// @Param end_time query string false "End time (Unix timestamp)"
// @Success 200 {object} services.ProjectDataResponse "Project data from Dune Analytics"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 500 {object} map[string]interface{} "Failed to get project data"
// @Router /dune/project-data/:id [get]
func (h *Handler) GetDuneProjectData(c *gin.Context) {
	idStr := c.Param("id")

	// Validate required parameters
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "id is required"})
		return
	}

	startTime, endTime, startDate, endDate, done := h.timeParse(c)
	if done {
		return
	}

	// Get project data
	projectData, err := h.duneService.GetProjectData(idStr, startDate, endDate)
	if err != nil {
		logEntry := log.Error().Err(err).Str("query id", idStr)
		if startTime != nil {
			logEntry = logEntry.Int64("start_time", *startTime)
		}
		if endTime != nil {
			logEntry = logEntry.Int64("end_time", *endTime)
		}
		logEntry.Msg("Failed to get project data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project data"})
		return
	}

	c.JSON(http.StatusOK, projectData)
}

// GetDuneProjectDataByTwitterUser godoc
// @Summary Get project data by Twitter user and category
// @Description Retrieve Dune project data for a specific Twitter user and category ID with optional time range filtering
// @Tags dune
// @Accept json
// @Produce json
// @Param user_name path string true "Twitter username"
// @Param id path string true "Category ID"
// @Param start_time query int64 false "Start time as Unix timestamp"
// @Param end_time query int64 false "End time as Unix timestamp"
// @Success 200 {object} services.ProjectDataResponse "Project data"
// @Failure 400 {object} map[string]interface{} "Bad request - invalid parameters"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /dune/categories/{id}/project-data/{user_name} [get]
func (h *Handler) GetDuneProjectDataByTwitterUser(c *gin.Context) {
	userName := c.Param("user_name")
	categoryIdStr := c.Param("id")

	// Validate required parameters
	if userName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_name is required"})
		return
	}

	startTime, endTime, startDate, endDate, done := h.timeParse(c)
	if done {
		return
	}

	idStr, err := h.duneService.GetProjectId(userName, categoryIdStr)
	if err != nil {
		log.Error().Err(err).Str("user_name", userName).Str("category_id", categoryIdStr).Msg("Failed to get project id")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project id"})
		return
	}

	// Get project data
	projectData, err := h.duneService.GetProjectData(idStr, startDate, endDate)
	if err != nil {
		logEntry := log.Error().Err(err).Str("query id", idStr)
		if startTime != nil {
			logEntry = logEntry.Int64("start_time", *startTime)
		}
		if endTime != nil {
			logEntry = logEntry.Int64("end_time", *endTime)
		}
		logEntry.Msg("Failed to get project data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project data"})
		return
	}

	c.JSON(http.StatusOK, projectData)
}

func (h *Handler) timeParse(c *gin.Context) (*int64, *int64, time.Time, time.Time, bool) {
	// Parse optional time parameters
	var startTime, endTime *int64

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTimeVal, err := strconv.ParseInt(startTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("start_time", startTimeStr).Msg("Invalid start_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_time parameter. Must be a valid Unix timestamp."})
			return nil, nil, time.Time{}, time.Time{}, true
		} else {
			startTime = &startTimeVal
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTimeVal, err := strconv.ParseInt(endTimeStr, 10, 64); err != nil {
			log.Error().Err(err).Str("end_time", endTimeStr).Msg("Invalid end_time parameter")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_time parameter. Must be a valid Unix timestamp."})
			return nil, nil, time.Time{}, time.Time{}, true
		} else {
			endTime = &endTimeVal
		}
	}

	// Convert Unix timestamps to dates if provided, otherwise use default range
	var startDate, endDate time.Time
	if startTime != nil {
		startDate = time.Unix(*startTime, 0)
	} else {
		// Default to 1 year ago if no start time provided
		startDate = time.Now().AddDate(-1, 0, 0)
	}
	if endTime != nil {
		endDate = time.Unix(*endTime, 0)
	} else {
		// Default to current time if no end time provided
		endDate = time.Now()
	}
	return startTime, endTime, startDate, endDate, false
}

// GetAllProjectsDataWithPeriodComparison retrieves aggregated data for all binding projects with period comparison
// @Summary Get aggregated data for all Dune projects with period comparison
// @Description Retrieve aggregated data for all Twitter-Dune binding projects within specified days range and compare with previous period
// @Tags dune
// @Accept json
// @Produce json
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days)"
// @Success 200 {array} services.ProjectDataResponse "Projects aggregated data with period comparison"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/projects/period-comparison [get]
func (h *Handler) GetAllProjectsDataWithPeriodComparison(c *gin.Context) {
	// Parse required days parameter
	daysStr := c.Query("days")

	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid positive integer."})
		return
	}

	if days <= 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be positive")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be greater than 0"})
		return
	}

	log.Info().
		Int("days", days).
		Msg("Getting all projects data with period comparison")

	// Get projects data with period comparison
	projectsData, err := h.duneService.GetAllProjectsDataWithPeriodComparison(days)
	if err != nil {
		log.Error().Err(err).Int("days", days).Msg("Failed to get all projects data with period comparison")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects data"})
		return
	}

	c.JSON(http.StatusOK, projectsData)
}

// GetBindingFieldPeriodComparison retrieves aggregated data for a specific binding with period comparison
// @Summary Get binding project period comparison
// @Description Retrieve aggregated data for a specific binding within specified days range and compare with the previous period of the same length. Returns the same data structure as GetAllProjectsDataWithPeriodComparison but for a single binding. If field parameter is provided, only that field will be included in data and data_change_rates.
// @Tags dune
// @Accept json
// @Produce json
// @Param id path int true "Dune binding ID"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days)"
// @Param field query string false "Optional field name to filter data (if provided, only this field will be included in data and data_change_rates)"
// @Success 200 {object} services.ProjectDataResponse "Binding project aggregated data with period comparison"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 404 {object} map[string]interface{} "Binding not found"
// @Failure 500 {object} map[string]interface{} "Failed to get project data"
// @Router /dune/bindings/{id}/period-comparison [get]
func (h *Handler) GetBindingFieldPeriodComparison(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing binding id"})
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		log.Error().Err(err).Str("id", idStr).Msg("Invalid binding id")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid binding id"})
		return
	}

	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid positive integer."})
		return
	}

	if days <= 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be positive")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be greater than 0"})
		return
	}

	// Get optional field parameter for filtering
	field := strings.TrimSpace(c.Query("field"))

	log.Info().
		Int64("binding_id", id).
		Int("days", days).
		Str("field", field).
		Msg("Getting binding project data with period comparison")

	// Get binding project data with period comparison
	projectData, err := h.duneService.GetBindingProjectDataWithPeriodComparison(id, days, field)
	if err != nil {
		// Distinguish not found
		if strings.Contains(strings.ToLower(err.Error()), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "binding not found"})
			return
		}
		log.Error().Err(err).Int64("binding_id", id).Int("days", days).Str("field", field).Msg("Failed to get binding project data with period comparison")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get project data"})
		return
	}

	c.JSON(http.StatusOK, projectData)
}

// GetCategoryFieldPeriodComparison retrieves aggregated data for binding projects in a specific category with period comparison
// @Summary Get category projects period comparison
// @Description Retrieve aggregated data for binding projects in a specific category within specified days range and compare with the previous period of the same length. Returns the same data structure as GetAllProjectsDataWithPeriodComparison but filtered to category bindings. If field parameter is provided, only that field will be included in data and data_change_rates.
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days)"
// @Param field query string false "Optional field name to filter data (if provided, only this field will be included in data and data_change_rates)"
// @Success 200 {array} services.ProjectDataResponse "Category projects aggregated data with period comparison"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/categories/{id}/period-comparison [get]
func (h *Handler) GetCategoryFieldPeriodComparison(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing category id"})
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		log.Error().Err(err).Str("id", idStr).Msg("Invalid category id")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid category id"})
		return
	}

	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid positive integer."})
		return
	}

	if days <= 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be positive")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be greater than 0"})
		return
	}

	// Get optional field parameter for filtering
	field := strings.TrimSpace(c.Query("field"))

	log.Info().
		Int64("category_id", id).
		Int("days", days).
		Str("field", field).
		Msg("Getting category projects data with period comparison")

	// Get category projects data with period comparison
	projectsData, err := h.duneService.GetCategoryProjectsDataWithPeriodComparison(id, days, field)
	if err != nil {
		// Distinguish not found
		if strings.Contains(strings.ToLower(err.Error()), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "category not found or no bindings found"})
			return
		}
		log.Error().Err(err).Int64("category_id", id).Int("days", days).Str("field", field).Msg("Failed to get category projects data with period comparison")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects data"})
		return
	}

	c.JSON(http.StatusOK, projectsData)
}

// GetAllProjectsFieldSumComparison retrieves aggregated sum data for all projects with period comparison
// @Summary Get all projects field sum comparison
// @Description Retrieve aggregated sum data for all binding projects within specified days range and compare with the previous period of the same length. Returns sum values across all projects for the specified field.
// @Tags dune
// @Accept json
// @Produce json
// @Param field query string false "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days)"
// @Success 200 {object} services.AllProjectsFieldSumComparisonResponse "All projects field sum comparison data"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/all-projects/field-sum-comparison [get]
func (h *Handler) GetAllProjectsFieldSumComparison(c *gin.Context) {
	// Parse optional field parameter
	field := strings.TrimSpace(c.Query("field"))

	// Parse required days parameter
	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid positive integer."})
		return
	}

	if days <= 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be positive")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be greater than 0"})
		return
	}

	log.Info().
		Str("field", field).
		Int("days", days).
		Msg("Getting all projects field sum comparison")

	// Get all projects field sum comparison
	sumComparison, err := h.duneService.GetAllProjectsFieldSumComparison(field, days)
	if err != nil {
		log.Error().Err(err).Str("field", field).Int("days", days).Msg("Failed to get all projects field sum comparison")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects field sum comparison"})
		return
	}

	c.JSON(http.StatusOK, sumComparison)
}

// GetCategoryFieldSumComparison retrieves aggregated sum data for projects in a specific category with period comparison
// @Summary Get category projects field sum comparison
// @Description Retrieve aggregated sum data for binding projects in a specific category within specified days range and compare with the previous period of the same length. Returns sum values for the specified field or all fields if field is not provided.
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Param field query string false "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days)"
// @Success 200 {object} services.CategoryFieldSumComparisonResponse "Category projects field sum comparison data"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/categories/{id}/field-sum-comparison [get]
func (h *Handler) GetCategoryFieldSumComparison(c *gin.Context) {
	// Parse required category ID parameter
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing category id"})
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		log.Error().Err(err).Str("id", idStr).Msg("Invalid category id")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid category id"})
		return
	}

	// Parse optional field parameter
	field := strings.TrimSpace(c.Query("field"))

	// Parse required days parameter
	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid positive integer."})
		return
	}

	if days <= 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be positive")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be greater than 0"})
		return
	}

	log.Info().
		Int64("category_id", id).
		Str("field", field).
		Int("days", days).
		Msg("Getting category projects field sum comparison")

	// Get category projects field sum comparison
	sumComparison, err := h.duneService.GetCategoryFieldSumComparison(id, field, days)
	if err != nil {
		// Distinguish not found
		if strings.Contains(strings.ToLower(err.Error()), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "category not found or no bindings found"})
			return
		}
		log.Error().Err(err).Int64("category_id", id).Str("field", field).Int("days", days).Msg("Failed to get category projects field sum comparison")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get category projects field sum comparison"})
		return
	}

	c.JSON(http.StatusOK, sumComparison)
}

// GetAllProjectsDailyFieldSum retrieves daily aggregated sum data for all projects
// @Summary Get all projects daily field sum
// @Description Retrieve daily aggregated sum data for all binding projects within specified days range. Returns daily sum values for each field without comparison.
// @Tags dune
// @Accept json
// @Produce json
// @Param field query string false "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days, 30 for last 30 days, 0 for all available data)"
// @Success 200 {object} services.AllProjectsDailyFieldSumResponse "All projects daily field sum data"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/all-projects/daily-field-sum [get]
func (h *Handler) GetAllProjectsDailyFieldSum(c *gin.Context) {
	// Parse optional field parameter
	field := strings.TrimSpace(c.Query("field"))

	// Parse required days parameter
	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid integer."})
		return
	}

	if days < 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be non-negative")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be non-negative (0 for all data)"})
		return
	}

	log.Info().
		Str("field", field).
		Int("days", days).
		Msg("Getting all projects daily field sum")

	// Get all projects daily field sum
	dailySum, err := h.duneService.GetAllProjectsDailyFieldSum(field, days)
	if err != nil {
		log.Error().Err(err).Str("field", field).Int("days", days).Msg("Failed to get all projects daily field sum")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get projects daily field sum"})
		return
	}

	c.JSON(http.StatusOK, dailySum)
}

// GetCategoryDailyFieldSum retrieves daily aggregated sum data for projects in a specific category
// @Summary Get category projects daily field sum
// @Description Retrieve daily aggregated sum data for binding projects in a specific category within specified days range. Returns daily sum values for each field without comparison.
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Param field query string false "Field name to aggregate (e.g., contract_interaction, users, or any dynamic field). If not provided, returns aggregated data for all available fields"
// @Param days query int true "Number of days for the analysis period (e.g., 7 for last 7 days, 30 for last 30 days, 0 for all available data)"
// @Success 200 {object} services.CategoryDailyFieldSumResponse "Category projects daily field sum data"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to get projects data"
// @Router /dune/categories/{id}/daily-field-sum [get]
func (h *Handler) GetCategoryDailyFieldSum(c *gin.Context) {
	// Parse required category ID parameter
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "missing category id"})
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		log.Error().Err(err).Str("id", idStr).Msg("Invalid category id")
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid category id"})
		return
	}

	// Parse optional field parameter
	field := strings.TrimSpace(c.Query("field"))

	// Parse required days parameter
	daysStr := c.Query("days")
	if daysStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "days is required parameter"})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil {
		log.Error().Err(err).Str("days", daysStr).Msg("Invalid days parameter")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid days parameter. Must be a valid integer."})
		return
	}

	if days < 0 {
		log.Error().Int("days", days).Msg("Invalid days parameter: must be non-negative")
		c.JSON(http.StatusBadRequest, gin.H{"error": "days parameter must be non-negative (0 for all data)"})
		return
	}

	log.Info().
		Int64("category_id", id).
		Str("field", field).
		Int("days", days).
		Msg("Getting category projects daily field sum")

	// Get category projects daily field sum
	dailySum, err := h.duneService.GetCategoryDailyFieldSum(id, field, days)
	if err != nil {
		// Distinguish not found
		if strings.Contains(strings.ToLower(err.Error()), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "category not found or no bindings found"})
			return
		}
		log.Error().Err(err).Int64("category_id", id).Str("field", field).Int("days", days).Msg("Failed to get category projects daily field sum")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get category projects daily field sum"})
		return
	}

	c.JSON(http.StatusOK, dailySum)
}

// === Category Management API Handlers ===

// DuneCategoryRequest represents a request to create/update a category
type DuneCategoryRequest struct {
	Name        string                     `json:"name" binding:"required" example:"defi-metrics" description:"Category name"`
	Description string                     `json:"description" example:"DeFi protocol metrics and data" description:"Category description"`
	Fields      []DuneCategoryFieldRequest `json:"fields" binding:"required" description:"Field definitions"`
}

// DuneCategoryFieldRequest represents a field definition in a category request
type DuneCategoryFieldRequest struct {
	Key          string `json:"key" binding:"required" example:"total_value_locked" description:"Field key/name"`
	Type         string `json:"type" binding:"required" example:"float" description:"Field type (int, float, string, bool)"`
	Note         string `json:"note" example:"Total Value Locked in USD" description:"Field description/note"`
	Required     bool   `json:"required" example:"true" description:"Whether field is required"`
	AggregateSum bool   `json:"aggregate_sum" example:"true" description:"Whether to use sum aggregation for comparison (default: true)"`
}

// DuneBindingCategoryRequest represents a request to associate a binding with categories
type DuneBindingCategoryRequest struct {
	CategoryIDs []int64 `json:"category_ids" binding:"required" description:"List of category IDs to associate"`
}

// CreateDuneCategory creates a new dune category with fields
// @Summary Create a new dune category
// @Description Create a new category with field definitions for dune data parsing
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param request body DuneCategoryRequest true "Category creation request"
// @Success 201 {object} db.DuneCategory "Category created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 500 {object} map[string]interface{} "Failed to create category"
// @Router /dune/categories [post]
func (h *Handler) CreateDuneCategory(c *gin.Context) {
	var req DuneCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	// Convert request fields to database models
	fields := make([]db.DuneCategoryField, len(req.Fields))
	for i, fieldReq := range req.Fields {
		fields[i] = db.DuneCategoryField{
			Key:          fieldReq.Key,
			Type:         fieldReq.Type,
			Note:         fieldReq.Note,
			Required:     fieldReq.Required,
			AggregateSum: &fieldReq.AggregateSum,
		}
	}

	// Create category
	category, err := h.duneService.CreateCategory(req.Name, req.Description, fields)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create Dune category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

// GetDuneCategories retrieves all dune categories
// @Summary Get all dune categories
// @Description Retrieve all dune categories with their field definitions
// @Tags dune-categories
// @Accept json
// @Produce json
// @Success 200 {array} db.DuneCategory "List of categories"
// @Failure 500 {object} map[string]interface{} "Failed to get categories"
// @Router /dune/categories [get]
func (h *Handler) GetDuneCategories(c *gin.Context) {
	categories, err := h.duneService.GetAllCategories()
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Dune categories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get categories"})
		return
	}

	c.JSON(http.StatusOK, categories)
}

// GetDuneCategory retrieves a specific dune category by ID
// @Summary Get a specific dune category
// @Description Retrieve a specific dune category by its ID with field definitions
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Success 200 {object} db.DuneCategory "Dune category"
// @Failure 400 {object} map[string]interface{} "Invalid category ID"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to get category"
// @Router /dune/categories/{id} [get]
func (h *Handler) GetDuneCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	category, err := h.duneService.GetCategoryByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
			return
		}
		log.Error().Err(err).Int64("id", id).Msg("Failed to get Dune category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get category"})
		return
	}

	c.JSON(http.StatusOK, category)
}

// UpdateDuneCategory updates an existing dune category
// @Summary Update a dune category
// @Description Update an existing dune category and its field definitions
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Param request body DuneCategoryRequest true "Updated category data"
// @Success 200 {object} db.DuneCategory "Category updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to update category"
// @Router /dune/categories/{id} [put]
func (h *Handler) UpdateDuneCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req DuneCategoryRequest
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	// Convert request fields to database models
	fields := make([]db.DuneCategoryField, len(req.Fields))
	for i, fieldReq := range req.Fields {
		fields[i] = db.DuneCategoryField{
			Key:          fieldReq.Key,
			Type:         fieldReq.Type,
			Note:         fieldReq.Note,
			Required:     fieldReq.Required,
			AggregateSum: &fieldReq.AggregateSum,
		}
	}

	// Update category
	category, err := h.duneService.UpdateCategory(id, req.Name, req.Description, fields)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
			return
		}
		log.Error().Err(err).Int64("id", id).Msg("Failed to update Dune category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

// DeleteDuneCategory deletes a dune category
// @Summary Delete a dune category
// @Description Delete a dune category and all its associations
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Success 200 {object} map[string]interface{} "Category deleted successfully"
// @Failure 400 {object} map[string]interface{} "Invalid category ID"
// @Failure 404 {object} map[string]interface{} "Category not found"
// @Failure 500 {object} map[string]interface{} "Failed to delete category"
// @Router /dune/categories/{id} [delete]
func (h *Handler) DeleteDuneCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	err = h.duneService.DeleteCategory(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
			return
		}
		log.Error().Err(err).Int64("id", id).Msg("Failed to delete Dune category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "message": "Category deleted successfully"})
}

// AssociateBindingWithCategories associates a binding with multiple categories
// @Summary Associate binding with categories
// @Description Associate a dune binding with multiple categories
// @Tags dune-bindings
// @Accept json
// @Produce json
// @Param id path int true "Binding ID"
// @Param request body DuneBindingCategoryRequest true "Category association request"
// @Success 200 {object} map[string]interface{} "Association successful"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 404 {object} map[string]interface{} "Binding not found"
// @Failure 500 {object} map[string]interface{} "Failed to associate categories"
// @Router /dune/bindings/{id}/categories [put]
func (h *Handler) AssociateBindingWithCategories(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	var req DuneBindingCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body: " + err.Error()})
		return
	}

	err = h.duneService.AssociateBindingWithCategories(id, req.CategoryIDs)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		log.Error().Err(err).Int64("binding_id", id).Msg("Failed to associate binding with categories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to associate categories: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "message": "Categories associated successfully"})
}

// GetBindingWithCategories retrieves a binding with its associated categories
// @Summary Get binding with categories
// @Description Get a specific binding with its associated categories and field definitions
// @Tags dune-bindings
// @Accept json
// @Produce json
// @Param id path int true "Binding ID"
// @Success 200 {object} db.DuneTwitterBinding "Binding with categories"
// @Failure 400 {object} map[string]interface{} "Invalid binding ID"
// @Failure 404 {object} map[string]interface{} "Binding not found"
// @Failure 500 {object} map[string]interface{} "Failed to get binding"
// @Router /dune/bindings/{id}/categories [get]
func (h *Handler) GetBindingWithCategories(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid binding ID"})
		return
	}

	binding, err := h.duneService.GetBindingWithCategories(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Binding not found"})
			return
		}
		log.Error().Err(err).Int64("id", id).Msg("Failed to get binding with categories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get binding"})
		return
	}

	c.JSON(http.StatusOK, binding)
}

// GetBindingsByCategory retrieves all bindings associated with a category
// @Summary Get bindings by category
// @Description Get all bindings associated with a specific category
// @Tags dune-categories
// @Accept json
// @Produce json
// @Param id path int true "Category ID"
// @Success 200 {array} db.DuneTwitterBinding "List of bindings"
// @Failure 400 {object} map[string]interface{} "Invalid category ID"
// @Failure 500 {object} map[string]interface{} "Failed to get bindings"
// @Router /dune/categories/{id}/bindings [get]
func (h *Handler) GetBindingsByCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	bindings, err := h.duneService.GetBindingsByCategory(id)
	if err != nil {
		log.Error().Err(err).Int64("category_id", id).Msg("Failed to get bindings by category")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bindings"})
		return
	}

	c.JSON(http.StatusOK, bindings)
}

// fetchProjectInfoByTwitterUser fetches project information from cached collections by Twitter usernamethods
// equalStringSlices compares two string slices for equality
func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func (h *Handler) fetchProjectInfoByTwitterUser(twitterUserName string) (*TradeTokenInfo, error) {
	// Call the existing getCaByUserName method (line 1219)
	caResp, err := h.getCaByUserName(twitterUserName)
	if err != nil {
		return nil, fmt.Errorf("failed to get CA by username: %w", err)
	}

	if len(caResp.Data.Data) == 0 {
		return nil, fmt.Errorf("no CA data found for user: %s", twitterUserName)
	}

	// Try CA addresses from last to first until we find token info
	caList := caResp.Data.Data
	for i := len(caList) - 1; i >= 0; i-- {
		ca := caList[i]
		tokenResp, err := h.getTokenInfo(ca.Address)
		if err != nil {
			// Log error but continue to next CA
			continue
		}

		if len(tokenResp.Data.List) > 0 && tokenResp.Data.List[0].Address != "" {
			// Found valid token info, return the first token
			token := tokenResp.Data.List[0]
			return &token, nil
		}
	}

	// No valid token info found for any CA address
	return nil, fmt.Errorf("no token info found for any CA address of user: %s", twitterUserName)
}
