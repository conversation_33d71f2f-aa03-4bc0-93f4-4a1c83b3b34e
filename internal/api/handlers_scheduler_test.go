package api

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHandler_SchedulerLifecycle(t *testing.T) {
	// Create handler with nil Redis service for basic scheduler testing
	handler := NewHandler(nil, nil, nil, nil, nil)

	// Test scheduler start
	handler.StartScheduler()
	assert.NotNil(t, handler.scheduler)

	// Test scheduler stop
	handler.StopScheduler()
}

func TestHandler_CacheCollectionsData(t *testing.T) {
	// This test is skipped because it requires a properly initialized RedisService
	// In a real test environment, you would set up Redis properly
	t.Skip("Skipping Redis cache test - requires proper Redis service setup")
}

func TestHandler_FetchCollectionsData_MockServer(t *testing.T) {
	// Note: This test would need to be modified to use the mock server URL
	// For now, we'll skip the actual API call test since it requires modifying
	// the fetchCollectionsData method to accept a custom URL
	t.Skip("Skipping API test - would need URL injection for proper testing")
}

func TestCollectionsScheduledTask_FiltersByTags(t *testing.T) {
	// This test verifies the filtering logic in collectionsScheduledTask
	collections := []TradeTokenInfo{
		{
			Name: "Collection with tags",
			Tags: []struct {
				Name  string `json:"name"`
				Color string `json:"color"`
				Rank  int    `json:"rank"`
				Type  int    `json:"type"`
			}{
				{Name: "test", Color: "blue", Rank: 1, Type: 1},
			},
		},
		{
			Name: "Collection without tags",
			Tags: []struct {
				Name  string `json:"name"`
				Color string `json:"color"`
				Rank  int    `json:"rank"`
				Type  int    `json:"type"`
			}{},
		},
	}

	// Filter collections with tags (simulating the logic in collectionsScheduledTask)
	filteredCollections := make([]TradeTokenInfo, 0)
	for _, collection := range collections {
		if len(collection.Tags) > 0 {
			filteredCollections = append(filteredCollections, collection)
		}
	}

	// Verify filtering
	assert.Len(t, filteredCollections, 1)
	assert.Equal(t, "Collection with tags", filteredCollections[0].Name)
}

func TestHandler_GetTokenInfoFromCache(t *testing.T) {
	// Create handler with nil Redis service for basic testing
	handler := NewHandler(nil, nil, nil, nil, nil)

	// Test with nil Redis service - should fallback to API
	result, err := handler.getTokenInfoFromCache("0x123")

	// The method should handle the fallback gracefully
	// It may return an empty result or an error depending on API availability
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, result)
	} else {
		// If API call succeeds (even with empty result), that's also valid
		assert.NotNil(t, result)
		// The API might return different codes, so we just check that we got a response
		t.Logf("API returned code: %d, message: %s", result.Code, result.Msg)
	}
}

func TestTradeTokenInfo_TagsStructure(t *testing.T) {
	// Test that TradeTokenInfo has the correct Tags structure
	tokenInfo := TradeTokenInfo{
		Name:    "Test Token",
		Address: "0x123",
		Tags: []struct {
			Name  string `json:"name"`
			Color string `json:"color"`
			Rank  int    `json:"rank"`
			Type  int    `json:"type"`
		}{
			{Name: "defi", Color: "blue", Rank: 1, Type: 1},
			{Name: "gaming", Color: "green", Rank: 2, Type: 1},
		},
	}

	// Verify tags structure
	assert.Len(t, tokenInfo.Tags, 2)
	assert.Equal(t, "defi", tokenInfo.Tags[0].Name)
	assert.Equal(t, "gaming", tokenInfo.Tags[1].Name)
	assert.Equal(t, "blue", tokenInfo.Tags[0].Color)
	assert.Equal(t, "green", tokenInfo.Tags[1].Color)
}

func TestHandler_GetTagsFromCacheByTwitterUsername(t *testing.T) {
	// Create handler with nil Redis service for basic testing
	handler := NewHandler(nil, nil, nil, nil, nil)

	// Test with nil Redis service - should return empty tags
	tags := handler.getTagsFromCacheByTwitterUsername("test_user")

	// Should return empty slice when cache is not available
	assert.NotNil(t, tags)
	assert.Len(t, tags, 0)
}

func TestGetTagsFromCacheLogic(t *testing.T) {
	// Test the logic for extracting tags from collections
	collections := []TradeTokenInfo{
		{
			TwitterUsername: "user1",
			Tags: []struct {
				Name  string `json:"name"`
				Color string `json:"color"`
				Rank  int    `json:"rank"`
				Type  int    `json:"type"`
			}{
				{Name: "defi", Color: "blue", Rank: 1, Type: 1},
				{Name: "gaming", Color: "green", Rank: 2, Type: 1},
			},
		},
		{
			TwitterUsername: "user1", // Same user, different collection
			Tags: []struct {
				Name  string `json:"name"`
				Color string `json:"color"`
				Rank  int    `json:"rank"`
				Type  int    `json:"type"`
			}{
				{Name: "defi", Color: "blue", Rank: 1, Type: 1}, // Duplicate tag
				{Name: "nft", Color: "red", Rank: 3, Type: 1},   // New tag
			},
		},
		{
			TwitterUsername: "user2", // Different user
			Tags: []struct {
				Name  string `json:"name"`
				Color string `json:"color"`
				Rank  int    `json:"rank"`
				Type  int    `json:"type"`
			}{
				{Name: "ai", Color: "purple", Rank: 1, Type: 1},
			},
		},
	}

	// Simulate the tag extraction logic
	screenName := "user1"
	tagSet := make(map[string]bool)
	var tags []string

	for _, collection := range collections {
		if strings.EqualFold(collection.TwitterUsername, screenName) {
			for _, tag := range collection.Tags {
				if tag.Name != "" && !tagSet[tag.Name] {
					tagSet[tag.Name] = true
					tags = append(tags, tag.Name)
				}
			}
		}
	}

	// Verify results
	assert.Len(t, tags, 3) // Should have 3 unique tags: defi, gaming, nft
	assert.Contains(t, tags, "defi")
	assert.Contains(t, tags, "gaming")
	assert.Contains(t, tags, "nft")
	assert.NotContains(t, tags, "ai") // Should not contain tags from other users
}
