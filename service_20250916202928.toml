# Real-Time CA Service Configuration
# Copy this file to service.toml and update the values as needed

[server]
# Server address and port
address = ":8080"
# Server timeouts (in seconds)
read_timeout_sec = 30
shutdown_timeout_sec = 10
write_timeout_sec = 30
# Only start API server without background workers (true/false)
only_api = false # config by cmd
# Gin mode for the server (debug/release)
gin_mode = "debug"
# enable http server (true/false)
enable = true # config by cmd

[logging]
# Log level (debug, info, warn, error, fatal, panic)
level = "debug" # config by cmd
# Pretty logging for development (true/false)
pretty = true

[metrics]
# Enable Prometheus metrics (true/false)
enabled = true
# Metrics endpoint path
path = "/metrics"

[database]
# PostgreSQL connection details
# db_name = "scattering-dev"
# host = "simsports-web-dev.capsr7pl6vbx.ap-southeast-1.rds.amazonaws.com"
# password = "1zmM4YmFJuzcAE9Mc49E"
# port = 5432

db_name = "twitter-ca"
host = "***********"
password = "iyNyK3Dm@AjYCHWTm"
port = 5342

ssl_mode = "disable"
user = "postgres"
# migrate_on_startup (true/false)
migrate_on_startup = false

[social_data]
# API key for SocialData.tools (REQUIRED)
api_key = "2955|6poDCknyBD87mnWHxJZNGNawshry5kOl64r9TFAs36ba4357"
# Base URL for SocialData.tools API
base_url = "https://api.socialapi.me"
# Keyword to search for in tweets
# search_keyword = "Ai agent CA"
search_keywords = ["Ai agent CA"]
# Polling interval in seconds (how often to check for new tweets)
polling_interval_sec = 10
# Rate limiting - maximum requests per second to the API
requests_per_sec = 2 # 120 per minute
# Webhook configuration (for receiving push notifications instead of polling)
initial_lookback_hours = 0
webhook_enabled = true
webhook_endpoint = "/api/webhook/twitter"
webhook_secret_key = ""
# List of Twitter users to ignore when processing tweets
blacklisted_users = ["babyshark_agent", "realMOSSCoin"]
# Twitter lists to fetch tweets from (array of objects with id and type)
twitter_lists = [
    #  {id = "1928330914351960090", type = "KOLs"},
    { id = "1928022998428307902", type = "important" },
    # {id = "1927997031173476588", type = "Projects"},
]
# Periodic update settings for tweets
periodic_update_enabled = false # Enable/disable periodic tweet updates
periodic_update_interval_hours = 1 # Update interval in hours for recent tweets 0 is executed only once at startup, not in a loop
periodic_update_newer_than_hours = 48 # Tweets newer than this (in hours) will be updated more frequently
periodic_update_older_than_hours = 48 # Tweets older than this (in hours) will not be updated
periodic_update_run_once_only = true # If true, only run update once at startup; if false, run periodically based on interval
# AI re-classification settings
ai_reclassify_enabled = false # Enable/disable AI re-classification of tweets with is_others = true on startup
ai_reclassify_batch_size = 50 # Number of tweets to process in each batch during re-classification
ai_reclassify_timestamp = 1751719972 # 如果设置了该时间戳，服务将只对在指定时间之后创建的、标记为 is_others = true 的推文进行AI重新分类。

[dex_screener]
# Base URL for DexScreener API
base_url = "https://api.dexscreener.com"
# Rate limiting - maximum requests per second to the API
requests_per_sec = 5 # 300 per minute
# Update interval in minutes (how often to update token data)
update_interval_min = 10
# Comma-separated list of supported blockchain networks
supported_chains = "ethereum,bsc,base,solana"

[moralis]
# API key for Moralis Web3 API (REQUIRED)
api_key = "************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# Base URL for Moralis API
base_url = "https://deep-index.moralis.io/api/v2"
# Rate limiting - maximum requests per second to the API
requests_per_sec = 5 # 300 per minute

# [ai]
# # Enable AI features (true/false)
# enabled = true
# # OpenAI API key
# api_key = "********************************************************************************************************************************************************************" # mian
# # AI model to use
# model = "gpt-4.1-mini"
# # model = "grok-3-mini-beta"
# # model = "grok-3-mini-fast-beta"
# # model = "grok-3-fast-beta"
# # Custom base URL for OpenAI API (leave empty for default)
# # base_url = "https://api.x.ai/v1"
# # Maximum number of retries for AI requests
# max_retry = 3
# # Rate limiting - maximum requests per second to the API
# requests_per_sec = 6

[ai]
# Enable AI features (true/false)
enabled = true
# OpenAI API key
# api_key = "********************************************************************************************************************************************************************"
# api_key = "sk-vamnzobtougxtcikhicxpimiobsppnvokvdttqdnaunvywgp" siliconflow
api_key = "sk-e075b99068cb47ba819b1bd791c1a9fc"
# AI model to use
#model = "deepseek-ai/DeepSeek-V3"
# model = "gpt-4.1-mini"
model = "deepseek-chat"
# Custom base URL for OpenAI API (leave empty for default)
#base_url = "https://api.siliconflow.cn"
# base_url = "https://gateway.ai.cloudflare.com/v1/ed7250d46f01f5d802d802880fe344f0/scatting/openai"
base_url = "https://api.deepseek.com"
# Maximum number of retries for AI requests
max_retry = 3
# Rate limiting - maximum requests per second to the API
requests_per_sec = 6

[telegram]
# Telegram Bot Token (REQUIRED if enabled)
bot_token = "8082280791:AAG3yJ94HYAmAhCBFedUZzTD7tAY4g4kOF4"
# Telegram Chat ID to send messages to (REQUIRED if enabled)
chat_id = "637227693" # merlin
# chat_id = "2852154732" # merlin
# Enable Telegram notifications (true/false)
enabled = true

[telegramNotify]
# Telegram Bot Token (REQUIRED if enabled)
bot_token = "8082280791:AAG3yJ94HYAmAhCBFedUZzTD7tAY4g4kOF4"
# Telegram Chat ID to send messages to (REQUIRED if enabled)
chat_id = "637227693" # merlin
# chat_id = "2852154732" # merlin
# Enable Telegram notifications (true/false)
enabled = true


[redis]
# Redis connection details
host = "localhost"
port = 6379
password = "sOmE_sEcUrE_pAsS"
db = 0
# Connection pool settings
pool_size = 10
min_idle_conns = 2
max_retries = 3
# Timeout settings (in seconds)
dial_timeout_sec = 5
read_timeout_sec = 3
write_timeout_sec = 3
pool_timeout_sec = 4
idle_timeout_sec = 300
idle_check_frequency_sec = 60
# Distributed locking configuration
lock_timeout_sec = 30
lock_retry_delay_sec = 1
lock_max_retries = 10
lock_refresh_interval_sec = 10
lock_cleanup_timeout_sec = 30

# Key management configuration
key_prefix = "v2_"                    # Prefix for all Redis keys to avoid conflicts
cleanup_old_keys = true               # Whether to clean up old keys on startup
old_key_patterns = ["tweet_lock:*"]   # Patterns of old keys to clean up

[duplicate_detector]
# Enable duplicate content detection (true/false)
enabled = true
# Similarity threshold for considering content as duplicate (0.0-1.0)
similarity_threshold = 0.7
# Time window in days to check for duplicates (older tweets are ignored)
time_window_days = 30
# Maximum number of tweets to compare against for each new tweet
max_tweets_to_compare = 100

[dune]
# Enable Dune service (true/false)
enabled = true
# Dune API key (optional, for authenticated requests)
api_key = "g1KqfhugUCWSr9MfbyS20gQzq4F8RuUF"
# Dune API base URL
base_url = "https://api.dune.com"
# Request timeout in seconds
timeout_sec = 30
# Maximum number of retries for failed requests
max_retries = 3
# Delay between retries in seconds
retry_delay_sec = 1