-- Migration: Add contract_addresses and tags fields to dune_twitter_bindings table
-- This migration adds the contract_addresses and tags JSONB fields to support
-- storing contract addresses and tags associated with Dune Twitter bindings

-- Add contract_addresses field
ALTER TABLE public.dune_twitter_bindings 
ADD COLUMN IF NOT EXISTS contract_addresses jsonb DEFAULT '[]'::jsonb;

-- Add tags field
ALTER TABLE public.dune_twitter_bindings 
ADD COLUMN IF NOT EXISTS tags jsonb DEFAULT '[]'::jsonb;

-- Add comments for documentation
COMMENT ON COLUMN public.dune_twitter_bindings.contract_addresses IS 'JSON array of contract addresses associated with this binding';
COMMENT ON COLUMN public.dune_twitter_bindings.tags IS 'JSON array of tags associated with this binding';