-- Migration: Add aggregate_sum field to dune_category_fields table
-- This migration adds a boolean field to control whether field comparison should use sum aggregation
-- Default value is true to maintain backward compatibility

-- Add aggregate_sum column to dune_category_fields table
ALTER TABLE public.dune_category_fields 
ADD COLUMN IF NOT EXISTS aggregate_sum boolean DEFAULT true NOT NULL;

-- Add comment to explain the field
COMMENT ON COLUMN public.dune_category_fields.aggregate_sum IS 'Controls whether field comparison uses sum aggregation (true) or latest value comparison (false). Default is true for backward compatibility.';