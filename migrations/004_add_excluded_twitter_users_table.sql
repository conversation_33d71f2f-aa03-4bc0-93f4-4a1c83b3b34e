-- Migration: Add excluded_twitter_users table
-- This table stores Twitter users that should be excluded from statistics and API returns

CREATE TABLE IF NOT EXISTS excluded_twitter_users (
    id BIGSERIAL PRIMARY KEY,
    twitter_user_name VARCHAR(255) NOT NULL,
    reason TEXT,
    excluded_by VA<PERSON>HAR(255),
    excluded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique index on twitter_user_name
CREATE UNIQUE INDEX IF NOT EXISTS idx_excluded_twitter_users_username 
ON excluded_twitter_users (twitter_user_name);

-- Add comment to the table
COMMENT ON TABLE excluded_twitter_users IS 'Twitter users excluded from statistics and API returns';
COMMENT ON COLUMN excluded_twitter_users.twitter_user_name IS 'Twitter username (screen_name) to exclude';
COMMENT ON COLUMN excluded_twitter_users.reason IS 'Reason for exclusion';
COMMENT ON COLUMN excluded_twitter_users.excluded_by IS 'Who excluded this user';
COMMENT ON COLUMN excluded_twitter_users.excluded_at IS 'When the user was excluded';