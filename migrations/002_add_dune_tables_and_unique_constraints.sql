-- Migration: Add Dune tables and unique constraints for conflict prevention
-- This migration adds the dune_twitter_bindings and dune_query_results tables
-- and ensures unique constraints on twitter_user_name and dune_query_id

-- Create dune_twitter_bindings table
create table if not exists public.dune_twitter_bindings
(
    id                bigserial
        primary key,
    created_at        timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at        timestamp with time zone default CURRENT_TIMESTAMP not null,
    dune_query_id     varchar(255)                                       not null,
    twitter_user_name varchar(255)                                       not null,
    chain_ids         jsonb                    default '[]'::jsonb,
    project_name      varchar(500),
    project_logo      text
);

alter table public.dune_twitter_bindings
    owner to postgres;

-- Create unique indexes to prevent conflicts
create unique index if not exists idx_dune_twitter_bindings_query_id_unique
    on public.dune_twitter_bindings (dune_query_id);

create unique index if not exists idx_dune_twitter_bindings_twitter_user_unique
    on public.dune_twitter_bindings (twitter_user_name);

-- Create dune_query_results table
create table if not exists public.dune_query_results
(
    id                    bigserial
        primary key,
    created_at            timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at            timestamp with time zone default CURRENT_TIMESTAMP not null,
    dune_query_id         varchar(255)                                       not null,
    query_date            date                                               not null,
    contract_interaction  integer                  default 0,
    users                 integer                  default 0
);

alter table public.dune_query_results
    owner to postgres;

-- Create indexes for dune_query_results
create index if not exists idx_dune_query_results_query_id
    on public.dune_query_results (dune_query_id);

create index if not exists idx_dune_query_results_date
    on public.dune_query_results (query_date);

-- Create composite unique index to prevent duplicate results for same query and date
create unique index if not exists idx_dune_query_results_query_date_unique
    on public.dune_query_results (dune_query_id, query_date);