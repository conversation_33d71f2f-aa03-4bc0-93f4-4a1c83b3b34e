.PHONY: build test run clean docker-build docker-run migrate config run-toml swag

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=ca-service
BINARY_UNIX=$(BINARY_NAME)_unix

# Build the application
build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./cmd/server

# Run tests
test:
	$(GOTEST) -v ./...

# Run the application
run:
	$(GOBUILD) -o $(BINARY_NAME) -v ./cmd/server
	./$(BINARY_NAME)

# Create a config file from the example
config:
	cp service.example.toml service.toml

# Run the application with TOML config
run-toml: build config
	./$(BINARY_NAME)

# Clean build files
clean:
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# Download dependencies
deps:
	$(GOMOD) download

# Build Docker image
docker-build:
	docker build -t real-time-ca-service .

# Run Docker container
docker-run:
	docker-compose up -d

# Stop Docker container
docker-stop:
	docker-compose down

# Run database migrations
migrate:
	psql -U postgres -c "CREATE DATABASE ca_service" || true
	psql -U postgres -d ca_service -f migrations/001_initial_schema.sql

# Run linter
lint:
	golangci-lint run

# Generate code coverage
coverage:
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

# Cross compilation for different platforms
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v ./cmd/server

# Generate Swagger documentation
swag:
	swag init --generalInfo main.go --dir ./cmd/server,./internal --parseDependency --parseInternal

# Help command
help:
	@echo "make build - Build the application"
	@echo "make test - Run tests"
	@echo "make run - Run the application"
	@echo "make clean - Clean build files"
	@echo "make deps - Download dependencies"
	@echo "make docker-build - Build Docker image"
	@echo "make docker-run - Run Docker container"
	@echo "make docker-stop - Stop Docker container"
	@echo "make migrate - Run database migrations"
	@echo "make lint - Run linter"
	@echo "make coverage - Generate code coverage"
	@echo "make build-linux - Cross compilation for Linux"
	@echo "make swag - Generate Swagger documentation"
	@echo "make help - Show this help message"
