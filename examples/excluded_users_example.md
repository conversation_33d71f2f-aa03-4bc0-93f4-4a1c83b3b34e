# 排除 Twitter 用户功能使用示例

## 场景描述

假设我们有一个项目 "OldProject" 的 Twitter 账号 `@old_project`，这个项目已经停止运营，但数据库中还有它的历史数据。我们希望在统计和 API 返回中不再显示这个用户的数据。

## 操作步骤

### 1. 查看当前统计数据

首先查看当前的公告统计，可能会看到 `old_project` 用户：

```bash
curl -X GET "http://localhost:8080/api/announcement-statistics" | jq '.[] | select(.screen_name == "old_project")'
```

### 2. 添加用户到排除列表

将 `old_project` 添加到排除列表：

```bash
curl -X POST "http://localhost:8080/api/excluded-twitter-users" \
  -H "Content-Type: application/json" \
  -d '{
    "twitter_user_name": "old_project",
    "reason": "项目已停止运营，不再追踪相关数据",
    "excluded_by": "admin"
  }'
```

**预期响应:**
```json
{
  "status": "success",
  "message": "Twitter user added to exclusion list"
}
```

### 3. 验证排除效果

再次查看公告统计，应该不再看到 `old_project` 用户：

```bash
curl -X GET "http://localhost:8080/api/announcement-statistics" | jq '.[] | select(.screen_name == "old_project")'
```

应该返回空结果。

### 4. 查看排除列表

确认用户已被添加到排除列表：

```bash
curl -X GET "http://localhost:8080/api/excluded-twitter-users" | jq '.'
```

**预期响应:**
```json
[
  {
    "id": 1,
    "twitter_user_name": "old_project",
    "reason": "项目已停止运营，不再追踪相关数据",
    "excluded_by": "admin",
    "excluded_at": "2025-01-21T10:30:00Z"
  }
]
```

### 5. 如果需要恢复用户

如果后来需要重新追踪这个用户，可以从排除列表中移除：

```bash
curl -X DELETE "http://localhost:8080/api/excluded-twitter-users/old_project"
```

**预期响应:**
```json
{
  "status": "success",
  "message": "Twitter user removed from exclusion list"
}
```

## 批量操作示例

### 批量添加多个用户

```bash
# 添加多个停止运营的项目
for user in "dead_project1" "inactive_project2" "old_token_project"; do
  curl -X POST "http://localhost:8080/api/excluded-twitter-users" \
    -H "Content-Type: application/json" \
    -d "{
      \"twitter_user_name\": \"$user\",
      \"reason\": \"项目停止运营\",
      \"excluded_by\": \"admin\"
    }"
  echo ""
done
```

### 查看排除效果

```bash
# 检查统计数据中是否还包含这些用户
curl -X GET "http://localhost:8080/api/announcement-statistics" | \
  jq '.[] | select(.screen_name | test("dead_project1|inactive_project2|old_token_project"))'
```

## 错误处理示例

### 重复添加用户

```bash
# 第一次添加
curl -X POST "http://localhost:8080/api/excluded-twitter-users" \
  -H "Content-Type: application/json" \
  -d '{
    "twitter_user_name": "test_user",
    "reason": "测试原因",
    "excluded_by": "admin"
  }'

# 再次添加同一用户（会更新现有记录）
curl -X POST "http://localhost:8080/api/excluded-twitter-users" \
  -H "Content-Type: application/json" \
  -d '{
    "twitter_user_name": "test_user",
    "reason": "更新的原因",
    "excluded_by": "admin2"
  }'
```

### 删除不存在的用户

```bash
curl -X DELETE "http://localhost:8080/api/excluded-twitter-users/nonexistent_user"
```

仍然会返回成功响应，因为删除操作是幂等的。

## 监控和审计

### 查看操作日志

在应用日志中可以看到相关操作记录：

```
INFO Twitter user added to exclusion list twitter_user_name=old_project excluded_by=admin
INFO Twitter user removed from exclusion list twitter_user_name=old_project
```

### 定期审查排除列表

建议定期审查排除列表，确保排除的用户仍然需要被排除：

```bash
# 获取排除列表并按排除时间排序
curl -X GET "http://localhost:8080/api/excluded-twitter-users" | \
  jq 'sort_by(.excluded_at) | .[] | {twitter_user_name, reason, excluded_by, excluded_at}'
```

## 注意事项

1. **用户名大小写**: Twitter 用户名是大小写敏感的，确保使用正确的大小写
2. **即时生效**: 排除操作立即生效，无需重启服务
3. **数据保留**: 原始推文数据不会被删除，只是在统计时被过滤
4. **API 一致性**: 所有相关的 API 都会自动应用排除逻辑