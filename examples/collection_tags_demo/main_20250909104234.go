package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/services"
)

// 演示 Collection Tags 缓存功能的使用
func main() {
	fmt.Println("Collection Tags Caching Demo")
	fmt.Println("============================")

	// 1. 初始化 Redis 服务
	redisConfig := config.RedisConfig{
		Host:     "localhost",
		Port:     6379,
		Password: "",
		DB:       0,
	}

	redisService, err := services.NewRedisService(redisConfig)
	if err != nil {
		log.Fatalf("Failed to initialize Redis service: %v", err)
	}
	defer redisService.Close()

	// 2. 初始化数据库（带 Redis 缓存）
	dbConfig := config.DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		User:     "postgres",
		Password: "postgres",
		DBName:   "ca_service",
		SSLMode:  "disable",
	}

	// 创建适配器
	adapter := &redisServiceAdapter{redisService}
	database, err := db.ConnectWithRedis(dbConfig, adapter)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close()

	// 3. 演示缓存功能
	fmt.Println("\n1. 第一次调用 - 从 API 获取并缓存")
	start := time.Now()
	validTags1, err := fetchValidTagsDemo(database)
	if err != nil {
		log.Printf("Error fetching valid tags: %v", err)
	} else {
		fmt.Printf("获取到 %d 个有效 tags，耗时: %v\n", len(validTags1), time.Since(start))
		fmt.Printf("示例 tags: %v\n", getFirstNTags(validTags1, 5))
	}

	fmt.Println("\n2. 第二次调用 - 从 Redis 缓存获取")
	start = time.Now()
	validTags2, err := fetchValidTagsDemo(database)
	if err != nil {
		log.Printf("Error fetching valid tags: %v", err)
	} else {
		fmt.Printf("获取到 %d 个有效 tags，耗时: %v\n", len(validTags2), time.Since(start))
		fmt.Printf("缓存命中，响应时间显著减少\n")
	}

	// 4. 演示 PopulateCollectionTags 过滤功能
	fmt.Println("\n3. 演示 Collection Tags 过滤")
	demoCollectionTagsFiltering(database)

	fmt.Println("\n演示完成！")
}

// redisServiceAdapter 适配器
type redisServiceAdapter struct {
	*services.RedisService
}

func (r *redisServiceAdapter) GetClient() interface{} {
	return r.RedisService.GetClient()
}

// 演示获取有效 tags
func fetchValidTagsDemo(database *db.Database) (map[string]bool, error) {
	// 注意：这是一个内部方法，通常不会直接调用
	// 在实际使用中，这个方法会在 SaveCollectionTags 和 PopulateCollectionTags 中自动调用
	// 这里仅用于演示缓存功能

	// 由于 fetchValidCollectionTags 是私有方法，我们无法直接调用
	// 在实际应用中，缓存功能会在以下场景自动触发：
	// 1. SaveCollectionTags() - 保存时验证
	// 2. PopulateCollectionTags() - 返回时过滤

	fmt.Println("注意：实际的缓存功能在 SaveCollectionTags 和 PopulateCollectionTags 中自动执行")
	return make(map[string]bool), nil
}

// 演示 Collection Tags 过滤
func demoCollectionTagsFiltering(database *db.Database) {
	// 创建模拟的 tweets 数据
	tweets := []*db.Tweet{
		{
			TweetID: "123456789",
			User: &db.TwitterUser{
				ScreenName: "test_user",
			},
			// CollectionTags 会在 PopulateCollectionTags 中填充
		},
	}

	fmt.Printf("处理前: tweets 数量 = %d\n", len(tweets))

	// 调用 PopulateCollectionTags 方法
	// 这个方法现在会自动过滤无效的 collection tags
	err := database.PopulateCollectionTags(tweets)
	if err != nil {
		log.Printf("Error populating collection tags: %v", err)
		return
	}

	fmt.Printf("处理后: tweets[0].CollectionTags = %v\n", tweets[0].CollectionTags)
	fmt.Println("注意：只有在 API 返回的有效 tags 列表中的 tags 才会被保留")
}

// 获取前 N 个 tags 用于演示
func getFirstNTags(tagMap map[string]bool, n int) []string {
	var tags []string
	count := 0
	for tag := range tagMap {
		if count >= n {
			break
		}
		tags = append(tags, tag)
		count++
	}
	return tags
}

// 演示缓存键检查
func checkCacheKey(redisService *services.RedisService) {
	client := redisService.GetClient()
	ctx := context.Background()

	// 检查缓存键是否存在
	exists, err := client.Exists(ctx, "v2_valid_collection_tags").Result()
	if err != nil {
		log.Printf("Error checking cache key: %v", err)
		return
	}

	if exists > 0 {
		fmt.Println("✓ 缓存键存在")

		// 获取 TTL
		ttl, err := client.TTL(ctx, "v2_valid_collection_tags").Result()
		if err != nil {
			log.Printf("Error getting TTL: %v", err)
		} else {
			fmt.Printf("✓ 缓存过期时间: %v\n", ttl)
		}
	} else {
		fmt.Println("✗ 缓存键不存在")
	}
}
