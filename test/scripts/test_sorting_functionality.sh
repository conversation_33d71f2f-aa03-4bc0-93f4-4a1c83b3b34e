#!/bin/bash

# Test script for the new sorting functionality in GetAnnouncementStatistics API

echo "Testing GetAnnouncementStatistics API sorting functionality..."
echo "============================================================"

BASE_URL="http://localhost:8080/api/announcement-statistics"

# Test 1: Basic endpoint without sorting (should work as before)
echo "1. Testing basic endpoint without sorting:"
curl -s "$BASE_URL" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Basic endpoint works"
else
    echo "✗ Basic endpoint failed"
fi

# Test 2: Test sorting by total announcements (desc)
echo "2. Testing sort by total announcements (desc):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=total&sort_direction=desc")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Sort by total (desc) works"
    # Check if first item has higher or equal total than second
    FIRST_TOTAL=$(echo "$RESPONSE" | jq -r '.[0].total_announcements_count // 0')
    SECOND_TOTAL=$(echo "$RESPONSE" | jq -r '.[1].total_announcements_count // 0')
    if [ "$FIRST_TOTAL" -ge "$SECOND_TOTAL" ]; then
        echo "✓ Descending order verified"
    else
        echo "✗ Descending order not working correctly"
    fi
else
    echo "✗ Sort by total (desc) failed"
fi

# Test 3: Test sorting by total announcements (asc)
echo "3. Testing sort by total announcements (asc):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=total&sort_direction=asc")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Sort by total (asc) works"
    # Check if first item has lower or equal total than second
    FIRST_TOTAL=$(echo "$RESPONSE" | jq -r '.[0].total_announcements_count // 0')
    SECOND_TOTAL=$(echo "$RESPONSE" | jq -r '.[1].total_announcements_count // 0')
    if [ "$FIRST_TOTAL" -le "$SECOND_TOTAL" ]; then
        echo "✓ Ascending order verified"
    else
        echo "✗ Ascending order not working correctly"
    fi
else
    echo "✗ Sort by total (asc) failed"
fi

# Test 4: Test default sort direction (should be desc when only sort_field is provided)
echo "4. Testing default sort direction (desc when only sort_field provided):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=total")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Default sort direction works"
    # Check if first item has higher or equal total than second (should be desc by default)
    FIRST_TOTAL=$(echo "$RESPONSE" | jq -r '.[0].total_announcements_count // 0')
    SECOND_TOTAL=$(echo "$RESPONSE" | jq -r '.[1].total_announcements_count // 0')
    if [ "$FIRST_TOTAL" -ge "$SECOND_TOTAL" ]; then
        echo "✓ Default descending order verified"
    else
        echo "✗ Default descending order not working correctly"
    fi
else
    echo "✗ Default sort direction failed"
fi

# Test 5: Test sorting by different fields
echo "5. Testing sort by different fields:"
FIELDS=("product_updates" "business_data" "ecosystem_partnership" "profit_opportunity" "industry_events" "others")
for field in "${FIELDS[@]}"; do
    RESPONSE=$(curl -s "$BASE_URL?sort_field=$field&sort_direction=desc")
    echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ Sort by $field works"
    else
        echo "✗ Sort by $field failed"
    fi
done

# Test 6: Test invalid sort_field
echo "6. Testing invalid sort_field:"
RESPONSE=$(curl -s "$BASE_URL?sort_field=invalid_field")
ERROR_MSG=$(echo "$RESPONSE" | jq -r '.error // ""')
if [[ "$ERROR_MSG" == *"Invalid sort_field"* ]]; then
    echo "✓ Invalid sort_field properly rejected"
else
    echo "✗ Invalid sort_field not properly handled"
fi

# Test 7: Test invalid sort_direction
echo "7. Testing invalid sort_direction:"
RESPONSE=$(curl -s "$BASE_URL?sort_field=total&sort_direction=invalid")
ERROR_MSG=$(echo "$RESPONSE" | jq -r '.error // ""')
if [[ "$ERROR_MSG" == *"Invalid sort_direction"* ]]; then
    echo "✓ Invalid sort_direction properly rejected"
else
    echo "✗ Invalid sort_direction not properly handled"
fi

# Test 8: Test sorting with time filtering
echo "8. Testing sorting with time filtering:"
CURRENT_TIME=$(date +%s)
YESTERDAY_TIME=$((CURRENT_TIME - 86400))
RESPONSE=$(curl -s "$BASE_URL?start_time=$YESTERDAY_TIME&end_time=$CURRENT_TIME&sort_field=total&sort_direction=desc")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Sorting with time filtering works"
else
    echo "✗ Sorting with time filtering failed"
fi

echo ""
echo "Testing completed!"
