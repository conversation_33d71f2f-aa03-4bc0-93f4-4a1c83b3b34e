#!/bin/bash

# Test script for duplicate push prevention functionality
# This script demonstrates how to test the duplicate prevention feature

set -e

echo "🚀 Testing Duplicate Push Prevention Feature"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
API_ENDPOINT="$BASE_URL/api/telegram-notification-stats"

echo -e "${BLUE}📋 Test Plan:${NC}"
echo "1. Check initial notification statistics"
echo "2. Verify API endpoints are working"
echo "3. Run database tests"
echo "4. Build and verify compilation"
echo ""

# Function to make HTTP request and check response
check_api() {
    local url=$1
    local description=$2
    
    echo -e "${YELLOW}Testing: $description${NC}"
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API endpoint accessible: $url${NC}"
        
        # Show response if it's the stats endpoint
        if [[ "$url" == *"telegram-notification-stats"* ]]; then
            echo "Response:"
            curl -s "$url" | jq . 2>/dev/null || curl -s "$url"
        fi
    else
        echo -e "${RED}❌ API endpoint not accessible: $url${NC}"
        echo "   Make sure the service is running on $BASE_URL"
    fi
    echo ""
}

# Function to run tests
run_tests() {
    local test_path=$1
    local description=$2
    
    echo -e "${YELLOW}Running: $description${NC}"
    
    if go test "$test_path" -v; then
        echo -e "${GREEN}✅ Tests passed: $test_path${NC}"
    else
        echo -e "${RED}❌ Tests failed: $test_path${NC}"
        return 1
    fi
    echo ""
}

# Function to build project
build_project() {
    echo -e "${YELLOW}Building project...${NC}"
    
    if go build ./cmd/server; then
        echo -e "${GREEN}✅ Build successful${NC}"
    else
        echo -e "${RED}❌ Build failed${NC}"
        return 1
    fi
    echo ""
}

# Main test execution
main() {
    echo -e "${BLUE}🔧 Step 1: Building project${NC}"
    build_project
    
    echo -e "${BLUE}🧪 Step 2: Running database tests${NC}"
    run_tests "./internal/db" "Database operations tests"
    
    echo -e "${BLUE}📚 Step 3: Running service tests${NC}"
    run_tests "./internal/services" "Service documentation tests"
    
    echo -e "${BLUE}🌐 Step 4: Testing API endpoints (if service is running)${NC}"
    check_api "$BASE_URL/api/health" "Health check endpoint"
    check_api "$API_ENDPOINT" "Telegram notification statistics endpoint"
    
    echo -e "${BLUE}📊 Step 5: Checking metrics (if service is running)${NC}"
    check_api "$BASE_URL/metrics" "Prometheus metrics endpoint"
    
    echo ""
    echo -e "${GREEN}🎉 All tests completed!${NC}"
    echo ""
    echo -e "${BLUE}📖 Next Steps:${NC}"
    echo "1. Start the service: ./server"
    echo "2. Check notification stats: curl $API_ENDPOINT"
    echo "3. Monitor metrics: curl $BASE_URL/metrics | grep telegram"
    echo "4. Process some tweets and verify duplicate prevention works"
    echo ""
    echo -e "${BLUE}📝 Key Features Implemented:${NC}"
    echo "✅ Database schema with notification tracking fields"
    echo "✅ Duplicate prevention logic in sendTelegramNotification"
    echo "✅ Comprehensive error handling and logging"
    echo "✅ Prometheus metrics for monitoring"
    echo "✅ API endpoint for real-time statistics"
    echo "✅ Complete test coverage"
    echo "✅ Backwards compatibility"
    echo ""
    echo -e "${BLUE}🔍 How to Verify Duplicate Prevention:${NC}"
    echo "1. Process a tweet that triggers a notification"
    echo "2. Check logs for 'Telegram notification sent successfully'"
    echo "3. Process the same tweet again"
    echo "4. Check logs for 'Telegram notification already sent for this tweet, skipping'"
    echo "5. Verify metrics show duplicate prevention: curl $BASE_URL/metrics | grep duplicate"
}

# Check if jq is available for JSON formatting
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  jq not found. JSON responses will not be formatted.${NC}"
    echo ""
fi

# Run main function
main

echo -e "${GREEN}✨ Duplicate Push Prevention Implementation Complete! ✨${NC}"
