#!/bin/bash

# Test script for excluded Twitter users functionality

BASE_URL="http://localhost:8080/api"

echo "Testing Excluded Twitter Users API functionality..."
echo "=================================================="

# Test 1: Get current excluded users (should be empty initially)
echo "1. Getting current excluded users..."
curl -s -X GET "$BASE_URL/excluded-twitter-users" | jq '.'
echo ""

# Test 2: Add a user to exclusion list
echo "2. Adding user 'test_user' to exclusion list..."
curl -s -X POST "$BASE_URL/excluded-twitter-users" \
  -H "Content-Type: application/json" \
  -d '{
    "twitter_user_name": "test_user",
    "reason": "Test exclusion for development",
    "excluded_by": "admin"
  }' | jq '.'
echo ""

# Test 3: Get excluded users again (should show the added user)
echo "3. Getting excluded users after adding one..."
curl -s -X GET "$BASE_URL/excluded-twitter-users" | jq '.'
echo ""

# Test 4: Test announcement statistics (should exclude the test user)
echo "4. Getting announcement statistics (should exclude test_user)..."
curl -s -X GET "$BASE_URL/announcement-statistics" | jq '.[0:3]'  # Show first 3 results
echo ""

# Test 5: Remove user from exclusion list
echo "5. Removing 'test_user' from exclusion list..."
curl -s -X DELETE "$BASE_URL/excluded-twitter-users/test_user" | jq '.'
echo ""

# Test 6: Get excluded users again (should be empty)
echo "6. Getting excluded users after removal..."
curl -s -X GET "$BASE_URL/excluded-twitter-users" | jq '.'
echo ""

echo "Test completed!"