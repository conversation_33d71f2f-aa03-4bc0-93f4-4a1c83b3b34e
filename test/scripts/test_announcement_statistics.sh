#!/bin/bash

# Test script for the announcement statistics API endpoint
# This script tests the new /api/announcement-statistics endpoint

echo "Testing Announcement Statistics API Endpoint"
echo "============================================="

# Start Redis for testing
echo "Starting Redis container..."
docker run -d --name redis-test -p 6379:6379 redis:alpine redis-server --requirepass sOmE_sEcUrE_pAsS > /dev/null 2>&1

# Wait for Redis to start
sleep 2

# Start the server in background
echo "Starting server..."
/opt/homebrew/bin/go run ./cmd/server --enable-http > /dev/null 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 5

echo "Testing API endpoint..."

# Test the announcement statistics endpoint
echo "1. Testing /api/announcement-statistics endpoint:"
RESPONSE=$(curl -s "http://localhost:8080/api/announcement-statistics")
echo "Response received: ✓"

# Check if response is an array
if echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1; then
    echo "✓ Response is an array"
else
    echo "✗ Response is not an array"
fi

# Check if first element has expected fields
if echo "$RESPONSE" | jq -e '.[0].twitter_user' > /dev/null 2>&1; then
    echo "✓ twitter_user field present"
else
    echo "✗ twitter_user field missing"
fi

if echo "$RESPONSE" | jq -e '.[0].total_announcements_count' > /dev/null 2>&1; then
    echo "✓ total_announcements_count field present"
else
    echo "✗ total_announcements_count field missing"
fi

# Check announcement type count fields
EXPECTED_FIELDS=("product_updates_count" "business_data_count" "ecosystem_partnership_count" "profit_opportunity_count" "industry_events_count")

echo "2. Checking announcement type count fields:"
for field in "${EXPECTED_FIELDS[@]}"; do
    if echo "$RESPONSE" | jq -e ".[0].$field" > /dev/null 2>&1; then
        echo "✓ $field present"
    else
        echo "✗ $field missing"
    fi
done

# Display sample data
echo ""
echo "3. Sample response data:"
echo "$RESPONSE" | jq '.'

echo ""
echo "4. Testing time filtering functionality:"

# Get current timestamp and yesterday's timestamp for testing
CURRENT_TIME=$(date +%s)
YESTERDAY_TIME=$((CURRENT_TIME - 86400))

# Test with time filtering
echo "Testing with time range (last 24 hours):"
TIME_FILTERED_RESPONSE=$(curl -s "http://localhost:8080/api/announcement-statistics?start_time=$YESTERDAY_TIME&end_time=$CURRENT_TIME")
if echo "$TIME_FILTERED_RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1; then
    echo "✓ Time filtering returns array"
    TIME_FILTERED_COUNT=$(echo "$TIME_FILTERED_RESPONSE" | jq '. | length')
    echo "✓ Time filtered results: $TIME_FILTERED_COUNT users"
else
    echo "✗ Time filtering failed"
fi

# Test error handling - invalid timestamp
echo "Testing error handling (invalid timestamp):"
ERROR_RESPONSE=$(curl -s "http://localhost:8080/api/announcement-statistics?start_time=invalid")
if echo "$ERROR_RESPONSE" | jq -e '.error' > /dev/null 2>&1; then
    echo "✓ Invalid timestamp error handling works"
else
    echo "✗ Invalid timestamp error handling failed"
fi

# Test error handling - start_time > end_time
echo "Testing error handling (start_time > end_time):"
ERROR_RESPONSE2=$(curl -s "http://localhost:8080/api/announcement-statistics?start_time=$CURRENT_TIME&end_time=$YESTERDAY_TIME")
if echo "$ERROR_RESPONSE2" | jq -e '.error' > /dev/null 2>&1; then
    echo "✓ Time range validation works"
else
    echo "✗ Time range validation failed"
fi

echo ""
echo "5. Testing health endpoint:"
HEALTH_RESPONSE=$(curl -s "http://localhost:8080/api/health")
if echo "$HEALTH_RESPONSE" | jq -e '.status' > /dev/null 2>&1; then
    echo "✓ Health endpoint working"
else
    echo "✗ Health endpoint not working"
fi

# Cleanup
echo ""
echo "Cleaning up..."
kill $SERVER_PID > /dev/null 2>&1
docker stop redis-test > /dev/null 2>&1
docker rm redis-test > /dev/null 2>&1

echo "Test completed!"
