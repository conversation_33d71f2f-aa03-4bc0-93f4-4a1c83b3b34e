#!/bin/bash

# Test script for the manual Dune data fetch API endpoint
# Usage: ./test_dune_fetch_all.sh [base_url] [api_key]

# Default values
BASE_URL=${1:-"http://localhost:8080"}
API_KEY=${2:-""}

echo "Testing Dune Fetch All API endpoint..."
echo "Base URL: $BASE_URL"
echo "API Key: ${API_KEY:-"(none provided)"}"
echo ""

# Test endpoint URL
ENDPOINT="$BASE_URL/api/dune/fetch-all"

# Prepare headers
HEADERS=(-H "Content-Type: application/json")
if [ -n "$API_KEY" ]; then
    HEADERS+=(-H "X-API-Key: $API_KEY")
fi

echo "Making POST request to: $ENDPOINT"
echo ""

# Make the request
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
    "${HEADERS[@]}" \
    -X POST \
    "$ENDPOINT")

# Extract HTTP status code
http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')

echo "HTTP Status: $http_status"
echo "Response Body:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

# Check if request was successful
if [ "$http_status" = "200" ]; then
    echo "✅ SUCCESS: Manual Dune data fetch triggered successfully!"
    
    # Parse and display key information
    if command -v jq >/dev/null 2>&1; then
        status=$(echo "$response_body" | jq -r '.status // "unknown"')
        message=$(echo "$response_body" | jq -r '.message // "unknown"')
        query_count=$(echo "$response_body" | jq -r '.query_count // "unknown"')
        duration=$(echo "$response_body" | jq -r '.duration // "unknown"')
        triggered_at=$(echo "$response_body" | jq -r '.triggered_at // "unknown"')
        
        echo ""
        echo "📊 Details:"
        echo "  Status: $status"
        echo "  Message: $message"
        echo "  Query Count: $query_count"
        echo "  Response Duration: $duration"
        echo "  Triggered At: $triggered_at"
        
        if [ "$triggered_at" != "unknown" ] && [ "$triggered_at" != "null" ]; then
            # Convert timestamp to human readable format (if date command supports it)
            if date -r "$triggered_at" >/dev/null 2>&1; then
                human_time=$(date -r "$triggered_at" 2>/dev/null || echo "unknown")
                echo "  Triggered At (Human): $human_time"
            fi
        fi
    fi
else
    echo "❌ FAILED: Request failed with HTTP status $http_status"
    
    # Try to extract error message
    if command -v jq >/dev/null 2>&1; then
        error_msg=$(echo "$response_body" | jq -r '.error // .message // "unknown error"')
        echo "Error: $error_msg"
    fi
fi

echo ""
echo "💡 Tips:"
echo "  - Make sure the server is running on $BASE_URL"
echo "  - Check server logs for detailed information about the fetch process"
echo "  - The fetch operation runs asynchronously, so check logs for completion status"
echo "  - You can provide an API key as the second argument for additional security"
