#!/bin/bash

# Test script for the new days_since_last_tweet sorting functionality in GetAnnouncementStatistics API

echo "Testing days_since_last_tweet sorting functionality..."
echo "======================================================"

BASE_URL="http://localhost:8080/api/announcement-statistics"

# Test 1: Test sorting by days_since_last_tweet (desc)
echo "1. Testing sort by days_since_last_tweet (desc):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=days_since_last_tweet&sort_direction=desc")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Sort by days_since_last_tweet (desc) works"
    # Check if first item has higher or equal days_since_last_tweet than second
    FIRST_DAYS=$(echo "$RESPONSE" | jq -r '.[0].days_since_last_tweet // 0')
    SECOND_DAYS=$(echo "$RESPONSE" | jq -r '.[1].days_since_last_tweet // 0')
    if [ "$FIRST_DAYS" -ge "$SECOND_DAYS" ]; then
        echo "✓ Descending order verified (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    else
        echo "✗ Descending order not working correctly (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    fi
else
    echo "✗ Sort by days_since_last_tweet (desc) failed"
fi

echo ""

# Test 2: Test sorting by days_since_last_tweet (asc)
echo "2. Testing sort by days_since_last_tweet (asc):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=days_since_last_tweet&sort_direction=asc")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Sort by days_since_last_tweet (asc) works"
    # Check if first item has lower or equal days_since_last_tweet than second
    FIRST_DAYS=$(echo "$RESPONSE" | jq -r '.[0].days_since_last_tweet // 0')
    SECOND_DAYS=$(echo "$RESPONSE" | jq -r '.[1].days_since_last_tweet // 0')
    if [ "$FIRST_DAYS" -le "$SECOND_DAYS" ]; then
        echo "✓ Ascending order verified (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    else
        echo "✗ Ascending order not working correctly (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    fi
else
    echo "✗ Sort by days_since_last_tweet (asc) failed"
fi

echo ""

# Test 3: Test default sort direction for days_since_last_tweet (should be desc)
echo "3. Testing default sort direction for days_since_last_tweet (should be desc):"
RESPONSE=$(curl -s "$BASE_URL?sort_field=days_since_last_tweet")
echo "$RESPONSE" | jq -e 'type == "array"' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ Default sort direction works"
    # Check if it's sorted in descending order (default)
    FIRST_DAYS=$(echo "$RESPONSE" | jq -r '.[0].days_since_last_tweet // 0')
    SECOND_DAYS=$(echo "$RESPONSE" | jq -r '.[1].days_since_last_tweet // 0')
    if [ "$FIRST_DAYS" -ge "$SECOND_DAYS" ]; then
        echo "✓ Default descending order verified (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    else
        echo "✗ Default descending order not working correctly (first: $FIRST_DAYS, second: $SECOND_DAYS)"
    fi
else
    echo "✗ Default sort direction failed"
fi

echo ""

# Test 4: Test that days_since_last_tweet field is present in response
echo "4. Testing days_since_last_tweet field presence:"
RESPONSE=$(curl -s "$BASE_URL")
DAYS_FIELD_EXISTS=$(echo "$RESPONSE" | jq -e '.[0].days_since_last_tweet != null' > /dev/null 2>&1 && echo "true" || echo "false")
if [ "$DAYS_FIELD_EXISTS" = "true" ]; then
    echo "✓ days_since_last_tweet field is present in response"
else
    echo "✗ days_since_last_tweet field is missing from response"
fi

echo ""

# Test 5: Test invalid sort_field still works (should include days_since_last_tweet in error message)
echo "5. Testing updated error message for invalid sort_field:"
RESPONSE=$(curl -s "$BASE_URL?sort_field=invalid_field")
ERROR_MSG=$(echo "$RESPONSE" | jq -r '.error // ""')
if [[ "$ERROR_MSG" == *"days_since_last_tweet"* ]]; then
    echo "✓ Error message includes days_since_last_tweet"
else
    echo "✗ Error message doesn't include days_since_last_tweet"
    echo "Error message: $ERROR_MSG"
fi

echo ""
echo "Testing completed!"