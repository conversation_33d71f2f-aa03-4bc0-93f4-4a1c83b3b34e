#!/bin/bash

# Test script for the new category period comparison API
# This script tests the /api/dune/categories/{id}/period-comparison endpoint

BASE_URL="http://localhost:8080/api"

echo "Testing Dune Category Period Comparison API"
echo "==========================================="

# Test 1: Test with missing category id (should fail)
echo "1. Testing with missing category id (should fail):"
curl -s "$BASE_URL/dune/categories//period-comparison?field=contract_interaction&days=7" | jq .

echo -e "\n"

# Test 2: Test with invalid category id (should fail)
echo "2. Testing with invalid category id (should fail):"
curl -s "$BASE_URL/dune/categories/invalid/period-comparison?field=contract_interaction&days=7" | jq .

echo -e "\n"

# Test 3: Test with missing field parameter (should fail)
echo "3. Testing with missing field parameter (should fail):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?days=7" | jq .

echo -e "\n"

# Test 4: Test with missing days parameter (should fail)
echo "4. Testing with missing days parameter (should fail):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction" | jq .

echo -e "\n"

# Test 5: Test with invalid days parameter (should fail)
echo "5. Testing with invalid days parameter (should fail):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=invalid" | jq .

echo -e "\n"

# Test 6: Test with zero days (should fail)
echo "6. Testing with zero days (should fail):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=0" | jq .

echo -e "\n"

# Test 7: Test with negative days (should fail)
echo "7. Testing with negative days (should fail):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=-5" | jq .

echo -e "\n"

# Test 8: Test with non-existent category (should fail)
echo "8. Testing with non-existent category (should fail):"
curl -s "$BASE_URL/dune/categories/99999/period-comparison?field=contract_interaction&days=7" | jq .

echo -e "\n"

# Test 9: Test with valid category and contract_interaction field (should succeed)
echo "9. Testing with valid category and contract_interaction field (should succeed):"
echo "   Category ID: 1, Field: contract_interaction, Days: 7"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=7" | jq .

echo -e "\n"

# Test 10: Test with valid category and users field (should succeed)
echo "10. Testing with valid category and users field (should succeed):"
echo "    Category ID: 1, Field: users, Days: 7"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=users&days=7" | jq .

echo -e "\n"

# Test 11: Test with different time periods (should succeed)
echo "11. Testing with different time periods:"
echo "    a) 1 day period:"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=1" | jq .

echo -e "\n    b) 14 days period:"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=14" | jq .

echo -e "\n    c) 30 days period:"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=contract_interaction&days=30" | jq .

echo -e "\n"

# Test 12: Test with dynamic field (if category has custom fields)
echo "12. Testing with potential dynamic field (may fail if field doesn't exist):"
curl -s "$BASE_URL/dune/categories/1/period-comparison?field=custom_metric&days=7" | jq .

echo -e "\n"

echo "Test completed!"
echo ""
echo "Expected behavior:"
echo "- Tests 1-8 should return 400 Bad Request or 404 Not Found with error messages"
echo "- Tests 9-11 should return 200 OK with category field period comparison data"
echo "- Test 12 may succeed or fail depending on whether the category has custom fields"
echo ""
echo "Note: Make sure the server is running on $BASE_URL before running this test."
echo "Also ensure that category with ID 1 exists and has associated bindings."
echo ""
echo "Key features of the Category Period Comparison API:"
echo "- Aggregates data from all bindings associated with a category"
echo "- Supports both legacy fields (contract_interaction, users) and dynamic fields"
echo "- Returns category information along with aggregated metrics"
echo "- Calculates change rate between current and previous periods"
echo "- Includes bindings_count to show how many bindings contributed to the aggregation"
echo ""
echo "Example response format:"
echo '{'
echo '  "category_id": 1,'
echo '  "category_name": "DeFi Protocols",'
echo '  "category_description": "Decentralized Finance protocol metrics",'
echo '  "field": "contract_interaction",'
echo '  "days": 7,'
echo '  "bindings_count": 5,'
echo '  "current_sum": 15420.0,'
echo '  "previous_sum": 12350.0,'
echo '  "change_rate": 24.86'
echo '}'
echo ""
echo "To create test data, you can use the following APIs:"
echo "1. Create a category: POST $BASE_URL/dune/categories"
echo "2. Create bindings: POST $BASE_URL/dune/bindings"
echo "3. Associate bindings with category: PUT $BASE_URL/dune/bindings/{id}/categories"
echo "4. Fetch data: POST $BASE_URL/dune/fetch-all"
