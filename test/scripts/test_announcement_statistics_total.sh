#!/bin/bash

# Test script for the new announcement statistics total API endpoint
# This script tests the new /api/announcement-statistics/total endpoint

echo "Testing Announcement Statistics Total API Endpoint"
echo "===================================================="

# Base URL for the API
BASE_URL="http://localhost:8080/api/announcement-statistics/total"

echo "1. Testing GET /api/announcement-statistics/total"
echo "URL: $BASE_URL"
echo ""

# Make the API request
RESPONSE=$(curl -s -w "\n%{http_code}" "$BASE_URL")
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP Status Code: $HTTP_CODE"
echo "Response Body:"
echo "$RESPONSE_BODY" | jq . 2>/dev/null || echo "$RESPONSE_BODY"
echo ""

# Check if the response is successful
if [ "$HTTP_CODE" = "200" ]; then
    echo "✓ API request successful"
    
    # Check if response contains expected fields
    echo "2. Checking response structure:"
    
    EXPECTED_FIELDS=("total_announcements" "change_percentage_24h" "previous_day_total")
    
    for field in "${EXPECTED_FIELDS[@]}"; do
        if echo "$RESPONSE_BODY" | jq -e ".$field" > /dev/null 2>&1; then
            echo "✓ $field present"
        else
            echo "✗ $field missing"
        fi
    done
    
    echo ""
    echo "3. Sample data:"
    echo "Total Announcements: $(echo "$RESPONSE_BODY" | jq -r '.total_announcements // "N/A"')"
    echo "24H Change Percentage: $(echo "$RESPONSE_BODY" | jq -r '.change_percentage_24h // "N/A"')%"
    echo "Previous Day Total: $(echo "$RESPONSE_BODY" | jq -r '.previous_day_total // "N/A"')"
    
else
    echo "✗ API request failed with status code: $HTTP_CODE"
    echo "Response: $RESPONSE_BODY"
fi

echo ""
echo "Test completed."