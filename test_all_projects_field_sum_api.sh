#!/bin/bash

# Test script for the new All Projects Field Sum Comparison API

BASE_URL="http://localhost:8080/api"

echo "Testing All Projects Field Sum Comparison API"
echo "================================================"

# Test 1: Test with contract_interaction field and 7 days
echo "1. Testing with contract_interaction field and 7 days:"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=contract_interaction&days=7" | jq .

echo -e "\n"

# Test 2: Test with users field and 14 days
echo "2. Testing with users field and 14 days:"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=users&days=14" | jq .

echo -e "\n"

# Test 3: Test with missing field parameter (should return all fields)
echo "3. Testing with missing field parameter (should return all fields):"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?days=7" | jq .

echo -e "\n"

# Test 4: Test with missing days parameter (should fail)
echo "4. Testing with missing days parameter (should fail):"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=contract_interaction" | jq .

echo -e "\n"

# Test 5: Test with invalid days parameter (should fail)
echo "5. Testing with invalid days parameter (should fail):"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=contract_interaction&days=0" | jq .

echo -e "\n"

# Test 6: Test with negative days (should fail)
echo "6. Testing with negative days (should fail):"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=contract_interaction&days=-1" | jq .

echo -e "\n"

# Test 7: Test with a dynamic field (if exists)
echo "7. Testing with potential dynamic field:"
curl -s "$BASE_URL/dune/all-projects/field-sum-comparison?field=total_value_locked&days=7" | jq .

echo -e "\n"

echo "Testing completed!"