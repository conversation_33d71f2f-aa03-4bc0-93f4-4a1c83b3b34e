package main

import (
	"context"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"

	"real-time-ca-service/internal/ai"
	"real-time-ca-service/internal/api"
	"real-time-ca-service/internal/config"
	"real-time-ca-service/internal/db"
	"real-time-ca-service/internal/logger"
	"real-time-ca-service/internal/services"

	_ "real-time-ca-service/docs"
)

// redisServiceAdapter adapts services.RedisService to db.RedisService interface
type redisServiceAdapter struct {
	*services.RedisService
}

// GetClient returns the Redis client as interface{}
func (r *redisServiceAdapter) GetClient() interface{} {
	return r.RedisService.GetClient()
}

// @title           Real-Time CA Service API
// @version         1.0
// @description     API for the Real-Time Contract Address service
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func main() {
	// Parse command-line flags
	flags := config.ParseCommandLineFlags()

	// Load configuration with command-line flag overrides
	cfg, err := config.LoadWithFlags(flags)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Initialize logger
	logger.Init(cfg.Logging.Level, cfg.Logging.Pretty)
	log.Info().Msg("Starting real-time CA service")

	// Initialize Redis service first (needed for database)
	redisService, err := services.NewRedisService(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis service")
	}
	defer redisService.Close()

	// Initialize database with Redis service
	database, err := db.ConnectWithRedis(cfg.Database, &redisServiceAdapter{redisService})
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}
	defer database.Close()

	// Run database migrations with GORM if enabled in config
	if cfg.Database.MigrateOnStartup {
		if err = db.MigrateDatabase(database); err != nil {
			log.Fatal().Err(err).Msg("Failed to run database migrations")
		}
		log.Info().Msg("Database migrations completed")
	} else {
		log.Info().Msg("Database migrations skipped due to configuration")
	}

	// Initialize AI service
	aiService := ai.NewService(ai.Config{
		Enabled:        cfg.AI.Enabled,
		APIKey:         cfg.AI.APIKey,
		Model:          cfg.AI.Model,
		BaseURL:        cfg.AI.BaseURL,
		MaxRetry:       cfg.AI.MaxRetry,
		RequestsPerSec: cfg.AI.RequestsPerSec,
	})

	// Check Redis environment for old keys
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := redisService.CheckRedisEnvironment(ctx); err != nil {
		log.Fatal().Err(err).Msg("Failed to check Redis environment")
	}

	// Initialize Telegram service
	telegramService := services.NewTelegramService(&cfg.Telegram)

	// Initialize shutdown manager with distributed lock manager first
	var shutdownManager *services.ShutdownManager
	var lockManager *services.DistributedLockManager
	if redisService != nil {
		lockManager = services.NewDistributedLockManager(redisService)
		shutdownManager = services.NewShutdownManager(lockManager, cfg.Server.ShutdownTimeout)
	} else {
		shutdownManager = services.NewShutdownManager(nil, cfg.Server.ShutdownTimeout)
	}

	// Initialize services
	twitterService := services.NewTwitterService(cfg.SocialData, database, aiService, telegramService, redisService, lockManager, cfg.DuplicateDetector)
	tokenService := services.NewTokenService(cfg.DexScreener, cfg.Moralis, database)
	caService := services.NewCAService(database, aiService, tokenService)
	duneService := services.NewDuneService(cfg.Dune, database)

	// Set up service dependencies
	twitterService.SetCAService(caService)
	tokenService.SetCAService(caService)

	// Register shutdown functions in reverse order of desired execution
	// (they will be executed in LIFO order)

	// Register HTTP server shutdown
	var server *http.Server
	var handler *api.Handler
	if cfg.Server.Enable {
		router := api.NewRouter(twitterService, caService, tokenService, redisService, duneService, cfg)
		// Extract handler from router for scheduler management
		handler = api.NewHandler(twitterService, caService, tokenService, redisService, duneService)

		server = &http.Server{
			Addr:         cfg.Server.Address,
			Handler:      router,
			ReadTimeout:  cfg.Server.ReadTimeout,
			WriteTimeout: cfg.Server.WriteTimeout,
		}

		shutdownManager.RegisterShutdownFunc(func() error {
			log.Info().Msg("Shutting down HTTP server...")
			ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
			defer cancel()
			return server.Shutdown(ctx)
		})

		go func() {
			log.Info().Str("address", cfg.Server.Address).Msg("Server starting")
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatal().Err(err).Msg("Failed to start server")
			}
		}()
	} else {
		log.Info().Msg("HTTP server not started due to configuration")
	}

	// Register scheduler shutdown
	if handler != nil {
		shutdownManager.RegisterShutdownFunc(func() error {
			log.Info().Msg("Stopping scheduler...")
			handler.StopScheduler()
			return nil
		})

		// Start scheduler
		log.Info().Msg("Starting scheduler...")
		handler.StartScheduler()
	}

	// Register background worker shutdown
	if !cfg.Server.OnlyAPI {
		shutdownManager.RegisterShutdownFunc(func() error {
			log.Info().Msg("Stopping background workers...")
			twitterService.StopMonitoring()
			tokenService.StopTokenUpdater()
			return nil
		})

		log.Info().Msg("Starting background workers...")
		twitterService.StartMonitoring()
		tokenService.StartTokenUpdater()
	} else {
		log.Info().Msg("Only API mode enabled, background workers not started")
	}

	// Start the shutdown manager signal handler
	// This will block until a shutdown signal is received
	shutdownManager.StartSignalHandler()

	// Wait for shutdown to complete
	shutdownManager.Wait()

	log.Info().Msg("Server exited properly")
}
